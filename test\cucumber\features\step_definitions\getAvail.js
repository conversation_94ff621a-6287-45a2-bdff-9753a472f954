const pactum = require('pactum');
const { Given, When, Then, Before } = require('@cucumber/cucumber');

let spec = pactum.spec();

Before(() => { spec = pactum.spec(); });

Given('I make a GET request with {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}', function (checkIn,checkOut,itemsPerPage,packageGroup,pageNumber,rooms,sortBy,sortOrder,zoneId) {
  spec.get("https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api/sync/avail?checkIn=" + checkIn + "/checkOut=" + checkOut + "/itemsPerPage=" + itemsPerPage + "/packageGroup=" + packageGroup + "/pageNumber=" + pageNumber + "/preferences=/rooms=" + rooms + "/sortBy=" + sortBy + "/sortOrder=" + sortOrder + "/validate=/zoneId=" + zoneId");
  spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.eSI7adU620BGiO2X8C3uVxkEvHdzhIJmyIuyegvB8ME');
  spec.withHeaders('gtw-sec-websocket-key', 'aHR0cDovLzE3Mi4zMC4xNTEuMjQ2OjgwfFFzTnUtQ2IxTmotcktPc0hBQWRK');
  spec.withHeaders('gtw-transaction-id', 'b74a9265-fdd0-297a-8527-c3d57fb16b95-1631194638376');
});

When('I receive a response', async function () {
  await spec.toss();
});

Then('response should have a status {int}', async function (code) {
  spec.response().should.have.status(code);
});

Then('response should have a cache time {int}', async function (code) {
  spec.response().should.have.expectResponseTime();
});

  it('should return response within cache time', async () => {

  var b = new Boolean(true);
  long responseTime1 = response.getTime();

  Given('I make a GET request with {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}', function (checkIn,checkOut,itemsPerPage,packageGroup,pageNumber,rooms,sortBy,sortOrder,zoneId) {
    spec.get("https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api/sync/avail?checkIn=" + checkIn + "/checkOut=" + checkOut + "/itemsPerPage=" + itemsPerPage + "/packageGroup=" + packageGroup + "/pageNumber=" + pageNumber + "/preferences=/rooms=" + rooms + "/sortBy=" + sortBy + "/sortOrder=" + sortOrder + "/validate=/zoneId=" + zoneId");
    spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.eSI7adU620BGiO2X8C3uVxkEvHdzhIJmyIuyegvB8ME');
    spec.withHeaders('gtw-sec-websocket-key', 'aHR0cDovLzE3Mi4zMC4xNTEuMjQ2OjgwfFFzTnUtQ2IxTmotcktPc0hBQWRK');
    spec.withHeaders('gtw-transaction-id', 'b74a9265-fdd0-297a-8527-c3d57fb16b95-1631194638376');}

  long responseTime2 = response.getTime();
    if(responseTime1 > responseTime2)
        {
        b = new Boolean(true);
        }else{
        b = new Boolean(false);
        }
    assert.ok(b, 'Time request is correct')
 });