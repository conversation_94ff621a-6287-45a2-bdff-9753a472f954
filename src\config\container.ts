import "reflect-metadata";

import { Container } from "inversify";
import { interfaces, TYPE, InversifyRestifyServer } from "inversify-restify-utils";
import { TYPES } from "./types";
import { Server } from "../server";
import Log from "./log";
import { ConsulClient } from './consul';

import { Main } from "../controllers/main";

const container = new Container();

container.bind(TYPES.Log).to(Log);

container.bind<ConsulClient>(TYPES.ConsulClient).toConstantValue(new ConsulClient());

container.bind<interfaces.Controller>(TYPE.Controller).to(Main).whenTargetNamed("Main");

container.bind(TYPES.ServerRestify).toConstantValue
	(
		new InversifyRestifyServer
			(
				container,
				{
					name: "sub-backend-hotels-store"
				}
			)
	);

container.bind<Server>(TYPES.Server).to(Server);

export default container;
