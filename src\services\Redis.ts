import * as redis from "redis";
import Log from "../config/log";
import { getEnv } from "../utils";

class Redis {
	client;
	private logOnce = true;
	private log = new Log();
	//base unit EX = seconds, so:
	private thirtyMinutes = 60 * 30;

	constructor() {
		this.log.debug(`Redis SET default EXPIRE: ${this.thirtyMinutes} seconds`);
	}

	private async connect() {
		if (!this.client) {
			this.client = redis.createClient({ 
				//host: "aaacnbgjnqaj4nlj3edoovakpkkgiw3xeimjhc4hv2sudpienvgnqvq-p.redis.sa-saopaulo-1.oci.oraclecloud.com", // RODAR LOCAL PRODUÇÃO
				host: getEnv("REDIS_HOST"),
				port: getEnv("REDIS_PORT") || 6379,
				tls: true
			});

			this.client.on("error", (error) => {
				if (this.logOnce === true) {
					this.log.error("Could not connect to Red<PERSON>", error);
					this.logOnce = false;
				}
			});

			this.client.on("ready", (ready) => {
				this.log.debug("Redis is Ready", this.client.connection_options);
				this.logOnce = true;
			});
		}
	}

	private async ensureConnected() {
		await this.connect();
		if (!this.client.connected) {
			throw new Error("Redis client is not connected");
		}
	}

	async isConnected() {
		await this.connect();
		return this.client.connected;
	}

	async get(key) {
		await this.ensureConnected();
		return new Promise((resolve, reject) => {
			this.client.get(key, this.#fetchCallback.bind(null, resolve, reject));
		});
	}

	async set(key, value: string) {
		await this.ensureConnected();
		this.client.set(key, value, "EX", this.thirtyMinutes);
	}

	async hmset(key, value: any[]) {
		await this.ensureConnected();
		this.client.hmset(key, ...value);
	}

	async hgetall(key) {
		await this.ensureConnected();
		return new Promise((resolve, reject) => {
			this.client.hgetall(key, this.#fetchCallback.bind(null, resolve, reject));
		});
	}

	#fetchCallback(resolve, reject, error, value) {
		if (error !== null) {
			reject(error);
			return;
		}
		resolve(value);
	}

	async fetchCached(key) {
		if (!!key === false) {
			this.log.debug("No key has been found to be used to fetch from Redis");
			return null;
		}
		return this.get(key)
			.then((value: string) => {
				return JSON.parse(value);
			})
			.catch((error) => {
				this.log.warn(
					`Redis isReady: ${this.client.ready}, isConnected: ${this.client.connected}`,
					`Could not fetch ${key} from Redis`
				);
				return null;
			});
	}

	setLog(log) {
		this.log = log;
	}
}

export default new Redis();