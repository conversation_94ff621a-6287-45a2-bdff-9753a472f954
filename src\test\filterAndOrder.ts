import "reflect-metadata";
import md5 from "md5";
import FilteringAndOrdering from "../FilteringAndOrdering";
import { foundAt } from "../FilteringAndOrdering/potential";

let avail = {hotels:[]}; 
let amenities = require("/home/<USER>/git/Opah/curls.responses/amenities.json");
//let avail = { hotels:[] };
let sample = require("../../src/test/toOrderSample.json");




(
	() =>
	{
		const a = FilteringAndOrdering.recurseToFindMany( amenities, foundAt.amenities.split(/\.\./) );
		console.log( a );
	}
)();


(
	() =>
	{
		const filteredAndOrdered = new FilteringAndOrdering({sortBy:"preferential", name:"c|a", minPrice:"300", maxPrice:"500"}, avail.hotels).getFilteredAndOrdered();
		filteredAndOrdered.forEach
		(
			each =>
			{
				console.log( each.name, each.rooms[0].rates[0].priceWithTax, each.isPreferential, each.rank );
			}
		)
	}
);



(
	() =>
	{
		console.log( FilteringAndOrdering.orderBy([{a:"Hello"}, {a:"Hi"}, {a:3}], ["a"], false) );
	}
);



(
	() =>
	{
		console.log( String(121.98).localeCompare(String(10.89)) );
	}
);


/*
(
	() =>
	{
		let ordered = FilteringAndOrdering.orderBy( avail.hotels, [ "rooms.0.rates.0.priceWithoutTax" ] );
		console.log( ordered );
	}
);
*/

(
	() =>
	{

		let ordered = FilteringAndOrdering.orderBy( sample, ["b.c.d", "a"] );
		console.log( "First for b.c.d then for a" );
		ordered.forEach
		(
			( each, key ) =>
			{
				console.log( each );
			}
		)

		ordered = FilteringAndOrdering.orderBy( sample, ["b.c.d"] );		
		console.log( "only for b.c.d" );
		ordered.forEach
		(
			( each, key ) =>
			{
				console.log( each );
			}
		);

		ordered = FilteringAndOrdering.orderBy( sample, ["b.c.e.0.a"] );		
		console.log( "only for b.c.e[0].a" );
		ordered.forEach
		(
			( each, key ) =>
			{
				console.log( each );
			}
		)

	}

);






