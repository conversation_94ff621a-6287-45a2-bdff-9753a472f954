import { Meta } from "../Types";
import FilteringAndOrdering from "../FilteringAndOrdering";
import { foundAt } from "../FilteringAndOrdering/potential";
import { error } from "console";

class MetaBuilder {
	private meta: Meta =
		{
			installment: (process.env.INSTALLMENT) ? parseInt(process.env.INSTALLMENT) : null,
			nightsNumber: 0,
			statusExecution:
			{
				started: new Date().toISOString(),
				total: 0,
				completedExecution: false,
				executed: 0
			},
			countHotels: 0,
			price:
			{
				minWithTax: null,
				maxWithTax: null,
				minWithoutTax: null,
				maxWithoutTax: null,
				currency: "BRL"
			},
			mealPlan: [],
			lodging: [],
			awards: [],
			amenities: [],
			chain: [],
			regions: []
		}

	private emptyString = "";
	private splitDot = /\./;

	private awardsCounter =
		{
			0: { count: 0 },
			1: { count: 0 },
			2: { count: 0 },
			3: { count: 0 },
			4: { count: 0 },
			5: { count: 0 },
			6: { count: 0 }
		};

	private regionCounts = new Map<string, number>()

	constructor(total: number) {
		this.total = total;
		this.executed = total;
	}

	public comprise(hotelsCollection) {
		this.meta.countHotels = hotelsCollection.length;
		this.prices(hotelsCollection);
		this.awards(hotelsCollection);
		this.mealPlan(hotelsCollection);
		this.amenities(hotelsCollection);
		this.regions(hotelsCollection);
	}

	set total(value) {
		this.meta.statusExecution.total = value;
	}

	set executed(value) {
		this.meta.statusExecution.executed = value;
		this.checkDone();
	}

	set countHotels(count) {
		this.meta.countHotels = count;
	}

	private checkDone() {
		if (this.meta.statusExecution.executed >= this.meta.statusExecution.total) {
			this.meta.statusExecution.completedExecution = true;
		}
		else {
			this.meta.statusExecution.completedExecution = false;
		}
	}

	public increaseExecuted() {
		this.meta.statusExecution.executed++;
		this.checkDone();
	}

	public getMeta() {
		return this.meta;
	}

	private amenities(hotelsCollection): void {
		const statistics = {};
		for (const hotel of hotelsCollection) {
			const hotelAmenities = FilteringAndOrdering.recurseToFindMany
				(
					hotel,
					foundAt.amenities.split(FilteringAndOrdering.splitWithDoubleDot)
				);

			for (const amenity of hotelAmenities) {
				//
				const ASCIIAmenity = amenity.replace(FilteringAndOrdering.nonASCII, this.emptyString);
				if (ASCIIAmenity in statistics) {
					statistics[ASCIIAmenity].counter++;
				}
				else {
					statistics[ASCIIAmenity] = { counter: 1, raw: amenity }
				}
			}
		}

		const builtAmenities = [];

		for (const each in statistics) {
			const statistic = statistics[each];
			builtAmenities.push({ name: statistic.raw, amount: statistic.counter });
		}

		this.meta.amenities = builtAmenities;
	}

	private mealPlan(hotelsCollection): void {
		const metaMealPlan = [];
		const buildMealPlan = {};
		hotelsCollection.forEach
			(
				(hotel) => {
					const mealPlan = FilteringAndOrdering.recurseToFind
						(
							hotel,
							foundAt.mealPlan.split(this.splitDot)
						);
					if (mealPlan === null) {
						return true;
					}
					const mealPlanASCII = mealPlan.replace(/[^\w]/ig, "").toLowerCase();
					if (buildMealPlan[mealPlanASCII] === undefined) {
						buildMealPlan[mealPlanASCII] = { count: 1, raw: mealPlan }
					}
					else {
						buildMealPlan[mealPlanASCII].count++;
					}
				}
			);

		for (const i in buildMealPlan) {
			metaMealPlan.push({ name: buildMealPlan[i].raw, amount: buildMealPlan[i].count });
		}

		this.meta.mealPlan = metaMealPlan;
	}

	private awards(hotelsCollection): void {
		hotelsCollection.forEach
			(
				hotel => {
					const award = FilteringAndOrdering.recurseToFind
						(
							hotel,
							foundAt.awards.split(this.splitDot)
						);

					if (this.awardsCounter[award] !== undefined) {
						this.awardsCounter[award].count++;
					}
				}
			);

		this.meta.awards = [];

		for (let i in this.awardsCounter) {
			if (this.awardsCounter[i].count > 0) {
				this.meta.awards.push({ name: String(i), amount: this.awardsCounter[i].count });
			}
		}

	}

	private prices(hotelsCollection): void {
		hotelsCollection.forEach
			(
				(hotel, index) => {
					const withAndWithoutTax = this.withAndWithoutTax(hotel);

					if (this.meta.price.minWithTax === null && withAndWithoutTax[0] !== null) {
						this.meta.price.minWithTax = withAndWithoutTax[0];
						this.meta.price.maxWithTax = withAndWithoutTax[0];
					}

					if (withAndWithoutTax[0] < this.meta.price.minWithTax) {
						this.meta.price.minWithTax = withAndWithoutTax[0];
					}
					else if (withAndWithoutTax[0] > this.meta.price.maxWithTax) {
						this.meta.price.maxWithTax = withAndWithoutTax[0];
					}

					if (this.meta.price.minWithoutTax === null && withAndWithoutTax[1] !== null) {
						this.meta.price.minWithoutTax = withAndWithoutTax[1];
						this.meta.price.maxWithoutTax = withAndWithoutTax[1];
					}

					if (withAndWithoutTax[1] < this.meta.price.minWithoutTax) {
						this.meta.price.minWithoutTax = withAndWithoutTax[1];
					}
					else if (withAndWithoutTax[1] > this.meta.price.maxWithoutTax) {
						this.meta.price.maxWithoutTax = withAndWithoutTax[1];
					}

				}
			)
	}

	private regions(hotelsCollection): void {

		this.meta.regions = [];

		try {

			hotelsCollection.forEach((hotel) => {

				if (hotel.resort && hotel.resort.location) {

					const { region } = hotel.resort.location;
					const key = JSON.stringify(region);
					if (this.regionCounts.has(key)) {
						this.regionCounts.set(key, this.regionCounts.get(key)! + 1)
					} else {
						this.regionCounts.set(key, 1);
					}

				}

			})

			this.regionCounts.forEach((amount, key) => {
				const region = JSON.parse(key);
				this.meta.regions.push({ ...region, amount });
			});


		} catch (err) {

			console.log("error", error)

		}
	}

	private withAndWithoutTax(hotel): number[] {
		const priceWithTax = FilteringAndOrdering.recurseToFind
			(
				hotel,
				foundAt.price.split(this.splitDot)
			);

		const priceWithoutTax = FilteringAndOrdering.recurseToFind
			(
				hotel,
				foundAt.priceWithoutTax.split(this.splitDot)
			);

		const numberedPriceWithTax = Number(priceWithTax)
		const numberedPriceWithoutTax = Number(priceWithoutTax)

		return (
			[
				numberedPriceWithTax,
				numberedPriceWithoutTax
			]
		);
	}
}


export default MetaBuilder;
