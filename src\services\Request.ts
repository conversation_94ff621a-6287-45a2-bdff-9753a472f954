

import log from "../config/log.js";
import axios from "axios";


class Request
{
	instance;
	
	constructor( baseURL: string )
	{
		this.instance = axios.create({baseURL: baseURL})
	}

	post( config )
	{
		return this.instance.request
		(
			{
				method:"post",
				...config
			}
		);
	}
	get( config )
	{
		return this.instance.request
		(
			{
				method:"get",
				...config
			}
		);
	}
}


export default Request;
