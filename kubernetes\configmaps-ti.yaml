apiVersion: v1
kind: ConfigMap
metadata:
  name: sub-backend-hotels-store
  namespace: corp-hotels
data:
  ADDITIONAL_OPTS: " "
  PREFIX: sub-backend-hotels-store
  VAULT_HOST: vault-dev.services.cvc.com.br
  VAULT_SCHEME: http
  CONSUL_HOST: consul-dev.services.cvc.com.br
  CONSUL_PORT: "8500"
  APP_NAME: sub-backend-hotels-store
  NODE_ENV: ti
  LOG_HOST: logstash-ti.services.cvc.com.br
  LOG_PORT: "12201"
  HOTEL_BACK_FOR_FRONT_GATEWAY_URL: http://hotel-back-for-front.k8s-ti-cvc.com.br/service
  HOTEL_BACK_FOR_FRONT_OSB_URL: http://hotel-back-for-front.k8s-ti-cvc.com.br/service
  BROKERS: "Extranet, Juniper, Omnibees, Jun<PERSON>Corp, Hotelbeds, CVC"
  CONSUL_URL: consul-dev.services.cvc.com.br
  CONSUL_KEY_VALUE_TREE: cvc-hosting-hotels-store
  CONSUL_KEY_FRANCHISE_SWITCH: turned.off.stores
  CONSUL_KEY_MASTER_SWITCH: master.switch
  CONSUL_KEY_PREFIX_ID_AIM_OSB: prefix.ids.aim.osb
  HEADER_NAME_LABEL_REDIS_KEY: gtw-search-token 
  HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN: gtw-sec-user-token
  HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY: gtw-sec-websocket-key
  WEBSOCKET_SERVER_URL: https://websocket-dev.services.cvc.com.br
  CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH: stores.importLoc