import { injectable, inject } from 'inversify';
import { TYPES } from '../config/types';
import restify from 'restify';
import { InversifyRestifyServer } from 'inversify-restify-utils';
import corsMiddleware from 'restify-cors-middleware';
const swaggerUi = require("swagger-ui-restify");
const swaggerDocument = require("../../swagger.document.json");
const uuid = require("uuid/v4");
import { ConsulClient } from '../config/consul';


@injectable()
export class Server {
	constructor
		(
			@inject(TYPES.ServerRestify)
			server: InversifyRestifyServer,

			@inject(TYPES.Log)
			log,

			@inject(TYPES.ConsulClient) consulClient: ConsulClient,
		) {

		let port = parseInt(process.env.PORT) || 8091;

		function removeParam(url: string, param: string): string {
			const regex = new RegExp(`[?&]${param}=[^&]*&?`);
			return url.replace(regex, '');
		}

		server.setConfig
			(
				(app) => {
					log.info(`Starting server on port: ${port}`, { operation: "SERVER" });
					const cors = corsMiddleware
						(
							{
								origins: ["*"]
							}
						);


					app.pre
						(
							function (req, res, next) {

								const transactionId = uuid();
								if (!req.headers["gtw-transaction-id"]) req.headers["gtw-transaction-id"] = transactionId;


								const initImportLoclUrl = req.url.match('/hotels/booking/rooms/');

								if (initImportLoclUrl !== null) {
									return next();
								}

								const initHasAvailUrl = req.url.match(/.hotels.\w+.rooms/);
								if (initHasAvailUrl === null) {
									return next();
								}

								

								let base64 = req.url.match(/(?<=\/)[^\/]+$/);

								if (base64 === null || /^rooms/.test(base64[0])) {


									return next();
								}



								const matchIgnorePriceChange = req.url.match(new RegExp(`[?&]ignorePriceChange=(true|false)`));
								req.url = removeParam(req.url, 'ignorePriceChange')
								if (matchIgnorePriceChange) req.params.ignorePriceChange = matchIgnorePriceChange[1];

								const matchpackageService = req.url.match(new RegExp(`[?&]packageService=(true|false)`));
								req.url = removeParam(req.url, 'packageService')
								if (matchpackageService) req.params.packageService = matchpackageService[1];

								base64 = req.url.match(/(?<=\/)[^\/]+$/);

								/^[a-zA-Z0-9+\/]+={0,2}$/.test(base64[0]) === true &&
									(req.headers.rateToken = base64[0]) &&
									(req.url = initHasAvailUrl[0]);

								// if(matchpackageService) 

								return next();

							}
						);

					app.use(function (req, res, next) {
						req.connection.setTimeout(60000);
						res.connection.setTimeout(60000);
						return next();
					});


					app.pre(cors.preflight);
					app.use(cors.actual);
					app.use(restify.plugins.acceptParser(app.acceptable));
					app.use(restify.plugins.queryParser());
					app.use(restify.plugins.bodyParser());

					const swaggerPath = "/api-doc";
					app.get(`${swaggerPath}/*`, swaggerUi.serve);
					app.get
						(
							swaggerPath,
							swaggerUi.setup
								(
									swaggerDocument, { explorer: true, baseURL: swaggerPath, path: swaggerPath }
								)
						);

					consulClient.scheduleConfigurations();

				}
			)
			.build()
			.listen
			(
				port, undefined, () => {
				log.info("Server started", { operation: "SERVER" });
			}
			);
	}
}
