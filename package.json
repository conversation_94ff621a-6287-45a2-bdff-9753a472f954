{"name": "sub-backend-hotels-store", "version": "3.0.15", "description": "BFF store", "main": "index.js", "scripts": {"dev:qa": "cross-env MODE=dev   LOG_HOST=logstash.services.cvc.com.br LOG_PORT=12201  PREFIX=sub-backend-hotels-store NODE_ENV=dev CONSUL_KEY_PREFIX_ID_AIM_OSB=prefix.ids.aim.osb  CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH=stores.importLoc CONSUL_KEY_MASTER_SWITCH=master.switch HOTEL_BACK_FOR_FRONT_GATEWAY_URL=http://hotel-back-for-front.k8s-qa-cvc.com.br HOTEL_BACK_FOR_FRONT_OSB_URL=http://*************:7011 BROKERS=Extranet,Juniper,Omnibees,JuniperCorp,Hotelbeds,CVC CONSUL_PORT=8500 CONSUL_URL=consul-qa.services.cvc.com.br CONSUL_HOST=consul-qa.services.cvc.com.br CONSUL_KEY_VALUE_TREE=sub-backend-hotels-store CONSUL_KEY_FRANCHISE_SWITCH=stores.switch REDIS_HOST=devtielasticache.tozgos.ng.0001.use1.cache.amazonaws.com REDIS_PORT=6379 HEADER_NAME_LABEL_REDIS_KEY=gtw-search-token HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY=gtw-sec-websocket-key HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN=gtw-sec-user-token WEBSOCKET_SERVER_URL=wss://websocket-hom.services.cvc.com.br AWS_REGION=us-east-1 node -r ts-node/register src/index.ts", "dev:ti": "cross-env MODE=dev       LOG_HOST=logstash-ti.services.cvc.com.br       LOG_PORT=12201        PREFIX=sub-backend-hotels-store     NODE_ENV=dev      CONSUL_KEY_PREFIX_ID_AIM_OSB=prefix.ids.aim.osb       CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH=stores.importLoc      CONSUL_KEY_MASTER_SWITCH=master.switch      HOTEL_BACK_FOR_FRONT_GATEWAY_URL=http://hotel-back-for-front.k8s-qa-cvc.com.br      HOTEL_BACK_FOR_FRONT_OSB_URL=http://*************:7011      BROKERS=Expedia,Juniper,Omnibees,JuniperCorp,Hotelbeds      CONSUL_PORT=8500      CONSUL_URL=consul-qa.services.cvc.com.br      CONSUL_KEY_VALUE_TREE=sub-backend-hotels-store      CONSUL_KEY_FRANCHISE_SWITCH=stores.switch      REDIS_HOST=qaelasticcache-001.z1vawg.0001.sae1.cache.amazonaws.com      REDIS_PORT=6379      HEADER_NAME_LABEL_REDIS_KEY=gtw-search-token      HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY=gtw-sec-websocket-key      HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN=gtw-sec-user-token      WEBSOCKET_SERVER_URL=https://websocket-hom.services.cvc.com.br       AWS_REGION=us-east-1 node -r ts-node/register src/index.ts", "dev:prod": "cross-env MODE=dev    PREFIX=sub-backend-hotels-store VAULT_HOST=vault.prod.cvc.intra CONSUL_HOST=consul.prod.cvc.intra  CONSUL_PORT=8500   APP_NAME=sub-backend-hotels-store   LOG_HOST=logstash.services.cvc.com.br  LOG_PORT=12201  HOTEL_BACK_FOR_FRONT_GATEWAY_URL=http://hotel-back-for-front.k8s-cvc.com.br   HOTEL_BACK_FOR_FRONT_OSB_URL=https://api.aws.cvc.com.br/bookinghotel   BROKERS=Extranet,Juniper,Omnibees,JuniperCorp,Hotelbeds,CVC    REDIS_HOST=cacheprdhotels-sp.xqdekl.ng.0001.sae1.cache.amazonaws.com    REDIS_PORT=6379    CONSUL_PORT=8500    CONSUL_URL=consul-prod.services.cvc.com.br    CONSUL_KEY_VALUE_TREE=sub-backend-hotels-store    CONSUL_KEY_FRANCHISE_SWITCH=stores.switch    CONSUL_KEY_MASTER_SWITCH=master.switch    CONSUL_KEY_PREFIX_ID_AIM_OSB=prefix.ids.aim.osb    HEADER_NAME_LABEL_REDIS_KEY=gtw-search-token     HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN=gtw-sec-user-token    HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY=gtw-sec-websocket-key    WEBSOCKET_SERVER_URL=https://b2b-websocket.services.cvc.com.br CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH=stores.importLoc node -r ts-node/register src/index.ts", "dev:prod:local": "cross-env MODE=dev    PREFIX=sub-backend-hotels-store VAULT_HOST=vault.prod.cvc.intra CONSUL_HOST=consul.prod.cvc.intra  CONSUL_PORT=8500   APP_NAME=sub-backend-hotels-store   LOG_HOST=logstash.services.cvc.com.br  LOG_PORT=12201  HOTEL_BACK_FOR_FRONT_GATEWAY_URL=http://hotel-back-for-front.k8s-cvc.com.br   HOTEL_BACK_FOR_FRONT_OSB_URL=https://api.aws.cvc.com.br/bookinghotel   BROKERS=Extranet,Juniper,Omnibees,JuniperCorp,Hotelbeds,CVC    REDIS_HOST=devtielasticache.tozgos.ng.0001.use1.cache.amazonaws.com REDIS_PORT=6379 HEADER_NAME_LABEL_REDIS_KEY=gtw-search-token    CONSUL_PORT=8500    CONSUL_URL=consul-prod.services.cvc.com.br    CONSUL_KEY_VALUE_TREE=sub-backend-hotels-store    CONSUL_KEY_FRANCHISE_SWITCH=stores.switch    CONSUL_KEY_MASTER_SWITCH=master.switch    CONSUL_KEY_PREFIX_ID_AIM_OSB=prefix.ids.aim.osb    HEADER_NAME_LABEL_REDIS_KEY=gtw-search-token     HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN=gtw-sec-user-token    HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY=gtw-sec-websocket-key    WEBSOCKET_SERVER_URL=https://b2b-websocket.services.cvc.com.br CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH=stores.importLoc node -r ts-node/register src/index.ts", "dev.watch.app": "nodemon --inspect --watch dist dist/index", "dev.watch.tsc": "tsc --watch", "start": "node dist/index.js", "clean": "rm -rf ./dist", "test.filterAndOrder": "tsc && node ./dist/test/filterAndOrder.js", "test.misc": "tsc && node ./dist/test/misc.js", "test.redis": "tsc && node ./dist/test/redis.js", "test.consul": "tsc && node ./dist/test/consul.js", "test.websocket": "tsc && node ./dist/test/websocket.js", "test.circular.json": "tsc && node ./dist/test/circular.json.js", "build": "tsc", "testCucumber": "node_modules/.bin/cucumber-js -f @cucumber/pretty-formatter test/cucumber/features/"}, "nodemonConfig": {"delay": 2000, "ignore": ["test/*"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@instana/collector": "^1.106.5", "axios": "^0.21.4", "base-64": "^1.0.0", "consul": "^0.40.0", "gelf": "^2.0.1", "inversify": "^4.13.0", "inversify-restify-utils": "^3.4.0", "jest": "^23.4.1", "jose": "^4.15.4", "json-stringify-safe": "^5.0.1", "latest-version": "^6.0.0", "md5": "^2.3.0", "moment": "^2.22.2", "redis": "^3.1.2", "reflect-metadata": "^0.1.12", "restify": "^7.2.1", "restify-cors-middleware": "^1.1.1", "socket.io-client": "^2.4.0", "swagger-ui-restify": "latest", "typescript": "^4.1.2", "uuid": "^3.3.2"}, "devDependencies": {"@cucumber/cucumber": "^7.3.1", "@cucumber/pretty-formatter": "*", "@types/lodash": "^4.14.172", "@types/node": "^10.17.48", "@types/redis": "^4.0.11", "@types/restify": "^7.2.3", "@types/restify-errors": "^4.3.3", "cross-env": "^5.2.0", "nodemon": "^2.0.12", "pactum": "^3.0.19", "restify-errors": "^8.0.2", "ts-node": "^7.0.0"}}