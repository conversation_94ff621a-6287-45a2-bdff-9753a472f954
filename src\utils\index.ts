//import container from "../config/container";
//import { TYPES } from "../config/types";
import Log from "../config/log";
import * as base64 from "base-64";
import md5 from "md5";
import MetaBuilder from "../MetaBuilder";
import { Consul } from "../services/Consul";

import FilteringAndOrdering from "../FilteringAndOrdering";
import Paging from "../Paging";

const log = new Log();

type Primitive = boolean | string | number
const emptyString = "";
const space = " ";


export const getEnv = (which) => {
	const envVar = process.env[which];

	if (envVar === undefined) {
		log.error(`${which} is not mapped!`);
	}

	return envVar;
}


export const getRedisKey = (pack) => {
	return pack[getEnv("HEADER_NAME_LABEL_REDIS_KEY")];
}

export const getBrokers = () => {
	return splitByCommonSeparators(getEnv("BROKERS"));
}

export const base64Decode = (what: string) => {
	try {
		const decoded = Buffer.from(what, "base64").toString();
		return decoded;
		// return base64.decode( what ).toString();
	}
	catch (error) {
		log.error(`Could not decode: ${what}, ${error.toString()}`);
		return null;
	}
}

export const openJWT = (JWT: string): null | object => {
	try {
		const jwtContent = JWT.split(/\./)[1];
		const stringJWT = base64Decode(jwtContent);
		let jwtContentDecoded = JSON.parse(stringJWT);
		log.debug(`Opened JWT: ${stringJWT}`);
		return jwtContentDecoded;
	}
	catch (error) {
		log.error(`Could not decode JWT: ${JWT}`)
		return null;
	}

}

export const fetchFromKnownJWT = (openedJWT: object, desiredFields: string[]): (object | Primitive)[] => {
	const response = [];

	for (let i = 0; i < desiredFields.length; i++) {
		const iter = desiredFields[i];
		const way = iter.split(/\./);
		const lookedFor = FilteringAndOrdering.recurseToFind(openedJWT, way);
		if (lookedFor === null) {
			return [];
		}
		response.push(lookedFor);
	}

	log.debug(`Fetched respectively: ${desiredFields.toString()}: ${response.toString()}`);

	return response;
}

export const fetchFromJWT = (pack, JWTKeyName, desiredFields: string[], why: string) => {
	const JWT = pack[JWTKeyName];
	if (!!JWT === false) {
		log.error
			(
				`JWT: ${JWTKeyName} has not been found, ` +
				`while searching to ${desiredFields.toString()}`
			);

		return [];
	}


	let jwtContentDecoded = openJWT(JWT);

	if (jwtContentDecoded === null) {
		return [];
	}

	log.debug
		(
			[
				`Searching for ${desiredFields.toString()},`,
				`so that ${why}`
			].join(" ")
		);


	return fetchFromKnownJWT(jwtContentDecoded, desiredFields);

}


const aimingToOSB = "aiming to OSB";
const eventCodeName = "eventCode";
const regexFirstTwoNumbers = /^\d{2}/;
const SPACE = " ";

const MASTER_SWITCH_ACTIVATED = /master.switch.activated/i;

export const shouldTargetNewGateway = (consul: Consul, branchId, req, importLoc: boolean = false): boolean => {

	const paramsId = req.params?.id;

	if (paramsId !== undefined) {
		const consulAimToOSB = consul.query(getEnv("CONSUL_KEY_PREFIX_ID_AIM_OSB"));
		if (consulAimToOSB !== null) {
			const eligibles = splitByCommonSeparators(consulAimToOSB);
			const getFirstTwo = String(paramsId).match(regexFirstTwoNumbers);

			if (getFirstTwo !== null) {
				if (eligibles.includes(getFirstTwo[0])) {
					log.debug
						(
							[
								`prefix.ids.aim.osb matched with ${getFirstTwo[0]}, as ${paramsId} starts with it,`,
								aimingToOSB
							].join(SPACE)
						);
					return false;
				}
				else {
					//					log.debug(getFirstTwo, paramsId);
				}
			}

		}
	}


	if (consul === null) {
		log.debug(`Consult to Consul has been dismissed, ${aimingToOSB}`);
		return false;
	}

	const eventCode = req.query[eventCodeName] || req.query[eventCodeName.toLowerCase()];

	if (eventCode !== undefined) {
		log.debug
			(
				[
					"Consult to Consul has been dismissed",
					`as eventCode(${eventCode}) is present, ${aimingToOSB}`
				].join(" ")
			);
		return false;
	}

	const masterSwitch = consul.query(getEnv("CONSUL_KEY_MASTER_SWITCH"));

	if (masterSwitch === "true") {
		log.debug("Master Switch is activated, aiming to new Gateway");
		return true;
	}


	const consulKeyFranchiseSwitch = getEnv("CONSUL_KEY_FRANCHISE_SWITCH");
	const franchises = consul.query(consulKeyFranchiseSwitch);

	if (franchises === null) {
		log.debug
			(
				[
					`Could not reach Consul key: ${consulKeyFranchiseSwitch}`,
					"to decide whether to reach OSB or the new Gateway,",
					"aiming to the default OSB"

				].join(" ")
			);

		return false;
	}

	const franchisesCollectionFromConsul = splitByCommonSeparators(franchises);

	if (!!branchId === false) {
		log.error(`branchId is not present, ${aimingToOSB}`)
		return false;
	}

	const consulKeyImportLocFranchiseSwitch = getEnv("CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH");
	const importLocFranchises = consul.query(consulKeyImportLocFranchiseSwitch);
	const importLocFranchisesCollectionFromConsul = splitByCommonSeparators(importLocFranchises);

	log.debug(`importLoc = ${importLoc}`);

	if (importLocFranchisesCollectionFromConsul.includes(branchId.toString()) && importLoc === true) {
		log.debug(`Aiming to new Gateway as branchId: ${branchId} has been found at Consul`)
		return true;
	}

	if (franchisesCollectionFromConsul.includes(branchId.toString())) {
		log.debug(`Aiming to new Gateway as branchId: ${branchId} has been found at Consul`)
		return true;
	}

	log.debug(`branchId: ${branchId} has NOT been found at Consul, ${aimingToOSB}`)

	return false;
}

const acceptedSeparators = /,|;|:|\||\s/

export const splitByCommonSeparators = (matter: string) => {

	return matter.split
		(
			acceptedSeparators
		)
		.map
		(
			matter => matter.trim().replace(acceptedSeparators, "").toUpperCase()
		)
		.filter
		(
			matter => !!matter
		);
};

const excludeHeaders = ["user-agent", "host", "accept", "postman-token"];

export const filterHeaders = (headers: { [index: string]: any }) => {
	const builtHeaders = {};

	for (const key in headers) {
		const lowered = key.toLowerCase();
		if (excludeHeaders.includes(lowered)) {
			continue;
		}
		builtHeaders[lowered] = headers[key];
	}

	return builtHeaders;
}


export const checkRedisCache = async (req, redis, bff) => {
	let redisKey = getRedisKey(req.headers);

	if (redisKey === undefined) {
		
		redisKey = bff.keySearch; // digestMD5UponPertinentFields(req, bff);
		
		log.debug
			(
				[
					"Although the search-token is not present at the headers,",
					"with the request query we were able to built it by our own:",
					redisKey
				].join(" ")
			);
	}

	const fetchedFromRedis = await redis.fetchCached(redisKey);

	if (fetchedFromRedis !== null) {
		log.debug(`Response has been found at Redis by ${redisKey}`);
		fetchedFromRedis.isCached = true;
		return fetchedFromRedis;
	}

	log.debug(`Response has NOT been found at Redis by ${redisKey}`);

	return {
		isCached: false
	}

}

export const buildRedisCache = (req, res, redis, data, bff): string => {

	if (redis.isConnected() === true) {
		const digestMD5 = digestMD5UponPertinentFields(req, bff);
		redis.set(digestMD5, JSON.stringify(data));
		log.debug(`Saved query to Redis by ${digestMD5}`);
		res.setHeader(getEnv("HEADER_NAME_LABEL_REDIS_KEY"), digestMD5);
		return digestMD5;
	}
	else {
		log.error("Redis is not connected");
	}
	return null;

}

const pertinentQueryFields =
	[
		"checkin", "checkout", "packagegroup",
		"rooms", "zoneid", "eventcode", "resorts", "inter"
	]

export function digestMD5UponPertinentFields(req, bff) {
	const agentSignAndBranchId = bff.getAgentSignAndBranchId();

	const withFields =
	{
		agentSign: agentSignAndBranchId[0],
		branchId: agentSignAndBranchId[1]
	}

	for (var i in req.query) {
		const prepared = i.toString().toLowerCase();
		if (pertinentQueryFields.includes(prepared)) {
			withFields[i] = req.query[i];
		}
	}

	log.debug(`Building Redis KEY with these fields: ${JSON.stringify(withFields)}`)
	const valueMd5 = md5(JSON.stringify(withFields));
	return valueMd5;
}

const forceNoCacheHeader = "ignore-redis";

export const thereIsCache = async (req, res, bff, redis) => {
	const transactionId: string = req.headers["gtw-transaction-id"].toString();

	if
		(
		req.headers[forceNoCacheHeader] === "true" ||
		parseInt(req.headers[forceNoCacheHeader]) > 0
	) {
		log.debug("Redis cache was forcefully ignored as \"ignore-redis\" is present at headers");
		return false;
	}
	const cache = await checkRedisCache(req, redis, bff);

	if(cache.isCached && bff.noWebsocket) {

		res.json(cache);
		return true;

	}else if (cache.isCached === true) {

		cache.mechanisms = { async: false, redis: true }

		if (!!cache.hotels === true) {
			const filteredAndOrdered = new FilteringAndOrdering(req.query, cache.hotels).getFilteredAndOrdered();
			const metaBuilder = new MetaBuilder(1);
			metaBuilder.comprise(cache.hotels);
			metaBuilder.countHotels = filteredAndOrdered.length;
			const filteredAndOrderedPaging = new Paging(req.query, filteredAndOrdered).getPagingResults();
			injectValidateOne(req.query, filteredAndOrderedPaging);
			res.json
				(
					{
						hotels: filteredAndOrderedPaging,
						meta: metaBuilder.getMeta(),
						mechanisms: cache.mechanisms,
						transactionId
					}
				);
		}
		else {
			cache['transactionId'] = transactionId;
			res.json(cache);
		}

		return true;
	}
	return false;

}

const channelManager =
{
	"34": "JUNIPER",
	"36": "EXPEDIA",
	"38": "OMNIBEES",
	"45": "CVC",
	"59": "EXTRANET",
	"61": "JUNIPERCORP",
	"63" : "WELCOMEBEDS"
}

export const injectValidateOne = (query, hotelsCollection: any[]): void => {
	if (parseInt(query.validate) !== 1) {
		return;
	}

	log.debug
		(
			[
				"Query arrived with validate = 1",
				"and because of that we are stating the broker as the channelManager"
			].join(space)
		);

	for (const hotel of hotelsCollection) {
		const brokerId = hotel?.id?.match(/^\d{2}/);
		if (!!brokerId === true && channelManager[brokerId[0]] !== undefined) {
			hotel.channelManager = channelManager[brokerId[0]];
		}
	}
}






