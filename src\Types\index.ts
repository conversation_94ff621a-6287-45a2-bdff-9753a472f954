
type MetaMealPlanAwardsAmenitiesChain = 
{
	code?: number,
	name: string,
	amount: number,
	count?: number
}

type Meta = 
{
	installment?: number,
	nightsNumber: number,
	statusExecution: 
	{
		started: string,
		total: number,
		completedExecution: boolean,
		executed: number
	},
	countHotels: number,
	price:
	{
		minWithTax: number,
		maxWithTax: number,
		minWithoutTax: number,
		maxWithoutTax: number,
		currency: string
	},
	mealPlan: MetaMealPlanAwardsAmenitiesChain[],
	lodging: any[],
	awards: MetaMealPlanAwardsAmenitiesChain[],
	amenities: MetaMealPlanAwardsAmenitiesChain[],
	chain: MetaMealPlanAwardsAmenitiesChain[]
	regions: MetaMealPlanAwardsAmenitiesChain[]
};

export { Meta, MetaMealPlanAwardsAmenitiesChain };
