apiVersion: apps/v1
kind: Deployment
metadata:
  name: sub-backend-hotels-store-deploy
  namespace: corp-hotels
  labels:
    app: sub-backend-hotels-store
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sub-backend-hotels-store
  template:
    metadata:
      labels:
        app: sub-backend-hotels-store
    spec:
      containers:
      - name: sub-backend-hotels-store
        image: 260584439167.dkr.ecr.sa-east-1.amazonaws.com/sub-backend-hotels-store:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "2"
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 35
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        env:
         - name: NODE_ENV
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: NODE_ENV
         - name: APP_NAME
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: APP_NAME
         - name: INSTANA_AGENT_HOST
           valueFrom:
             fieldRef:
               fieldPath: status.hostIP
         - name: VAULT_HOST
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: VAULT_HOST
         - name: VAULT_SCHEME
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: VAULT_SCHEME
         - name: CONSUL_HOST
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: CONSUL_HOST
         - name: CONSUL_PORT
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: CONSUL_PORT
         - name: LOG_HOST
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: LOG_HOST
         - name: LOG_PORT
           valueFrom:
             configMapKeyRef:
               name: sub-backend-hotels-store
               key: LOG_PORT
        envFrom:
          - configMapRef:
              name: sub-backend-hotels-store
          - secretRef:
              name: sub-backend-hotels-store
        ports:
        - containerPort: 80
        - containerPort: 5005
