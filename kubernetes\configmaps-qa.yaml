apiVersion: v1
kind: ConfigMap
metadata:
  name: sub-backend-hotels-store
  namespace: corp-hotels
data:
  ADDITIONAL_OPTS: " "
  PREFIX: sub-backend-hotels-store
  VAULT_HOST: vault.qa.cvc.intra
  VAULT_SCHEME: http
  CONSUL_HOST: consul.qa.cvc.intra
  CONSUL_PORT: "8500"
  APP_NAME: sub-backend-hotels-store
  NODE_ENV: qa
  LOG_HOST: logstash-qa.services.cvc.com.br
  LOG_PORT: "12201"
  HOTEL_BACK_FOR_FRONT_GATEWAY_URL: http://hotel-back-for-front.k8s-qa-cvc.com.br
  HOTEL_BACK_FOR_FRONT_OSB_URL: http://osb-qa.services.cvc.com.br
  BROKERS: "Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC, WELCOMEBEDS"
  REDIS_HOST: qaelasticcache-001.z1vawg.0001.sae1.cache.amazonaws.com
  REDIS_PORT: "6379"
  CONSUL_URL: consul-qa.services.cvc.com.br
  CONSUL_KEY_VALUE_TREE: sub-backend-hotels-store
  CONSUL_KEY_FRANCHISE_SWITCH: stores.switch
  CONSUL_KEY_MASTER_SWITCH: master.switch
  CONSUL_KEY_PREFIX_ID_AIM_OSB: prefix.ids.aim.osb
  HEADER_NAME_LABEL_REDIS_KEY: gtw-search-token 
  HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN: gtw-sec-user-token
  HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY: gtw-sec-websocket-key
  WEBSOCKET_SERVER_URL: https://websocket-hom.services.cvc.com.br
  CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH: stores.importLoc
