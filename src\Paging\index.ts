class Paging
{
	private itemsPerPage: number = null;
	private pageNumber: number = null;
  private collection = null;

	constructor( query, collection )
	{
		this.collection = collection;
    this.itemsPerPage = query.itemsPerPage ? parseInt(query.itemsPerPage) : null;
    this.pageNumber = query.pageNumber ? parseInt(query.pageNumber) : null;
	}

	public getPagingResults()
	{
    if(!this.itemsPerPage || !this.pageNumber) {
      return this.collection;
    }
    const startIndex: number = (this.pageNumber - 1) * this.itemsPerPage;
    const endIndex: number = this.pageNumber * this.itemsPerPage;
    const pagingCollection = this.collection.slice(startIndex, endIndex);
    return pagingCollection;
	}
}

export default Paging;
