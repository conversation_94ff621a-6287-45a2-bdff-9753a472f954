apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: sub-backend-hotels-store-hpa
  namespace: corp-hotels
  labels:
    app: sub-backend-hotels-store
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sub-backend-hotels-store-deploy
  minReplicas: 2
  maxReplicas: 30
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 100