

#!/bin/bash

#Be concerned about this vars only at local development deploys, otherwise do not mind them
#Also remember to add them at the kubernetes config maps if needed

#dev, qa, ti, prod

mode="dev"

export MODE=$mode
export NODE_ENV=$mode
#export HOTEL_BACK_FOR_FRONT_GATEWAY_URL="http://hotel-back-for-front.k8s-qa-cvc.com.br"
export HOTEL_BACK_FOR_FRONT_GATEWAY_URL="http://hotel-back-for-front.k8s-cvc.com.br"
#export HOTEL_BACK_FOR_FRONT_OSB_URL="http://osb-qa.services.cvc.com.br"
export HOTEL_BACK_FOR_FRONT_OSB_URL="https://api.aws.cvc.com.br/bookinghotel"
export BROKERS="Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC"
export CONSUL_PORT="8500"
#export CONSUL_URL="consul-dev.services.cvc.com.br"
export CONSUL_URL="consul-qa.services.cvc.com.br"
export CONSUL_KEY_VALUE_TREE="sub-backend-hotels-store"
export CONSUL_KEY_FRANCHISE_SWITCH="stores.switch"
export CONSUL_KEY_MASTER_SWITCH="master.switch"
export CONSUL_KEY_PREFIX_ID_AIM_OSB="prefix.ids.aim.osb"
#export REDIS_HOST="devtielasticache.tozgos.ng.0001.use1.cache.amazonaws.com"
export REDIS_HOST="localhost"
export REDIS_PORT=6379
export HEADER_NAME_LABEL_REDIS_KEY="gtw-search-token" 
export HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY="gtw-sec-websocket-key"
export HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN="gtw-sec-user-token"
export WEBSOCKET_SERVER_URL="https://websocket-hom.services.cvc.com.br"
