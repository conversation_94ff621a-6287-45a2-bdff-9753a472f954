Feature: Test rooms has avail

  Scenario Outline: Test rooms has avail
    Given I make a GET request rooms hasAvail with <idHotel>, <token>
    When I receive a response rooms hasAvail
    Then response rooms should hasAvail have a status 200
    Examples:
      | idHotel  | token    |
      | 381110   | PHJhdGVUb2tlbiBhZ3M9IkxPSiIgYWltPSJmYWxzZSIgYWlwPSJmYWxzZSIgYnJpPSIxMDAwIiBjYXQ9IjMyMzEiIGNpZD0iMzgwMDEiIGNtaT0iMzgiIGNtcD0iQmVkQnJlYWtmYXN0IiBjbXo9IjYzNzU1MzciIGNwcD0iMCIgY3VyPSJCUkwiIGR0Zj0iMjAyMi0wMy0wNyIgZHRpPSIyMDIyLTAzLTA1IiBlY3Q9IkJSIiBlc3Q9IkNFIiBleGM9ImZhbHNlIiBlemk9IjE0NjMiIGZlYj0iZmFsc2UiIGhvdD0iMzgxMTEwIiBsYW49InB0X0JSIiBta2k9IjI0NzM3MDQ4MSIgbWtwPSIwLjY3MCIgbXRkPSJIb3RlbEhhc0F2YWlsIiBwY3E9IjEuMDAwMDAwMDAwMCIgcGlkPSIzODExMTAiIHBrZz0iU1RBTkRBTE9ORSIgcGxhPSI1NiIgcGxjPSJCUkwiIHBsZD0iT01CIiBwb3Q9IjM5OS42MCIgcHB0PSJWb3VjaGVyIiBwcHg9IiIgcHB5PSI1ODY3ODkxIiBwcmQ9IkhPVCIgcHd0PSIzOTkuNjAiIHB4bz0iMzAsMzAiIHB4cz0iMzAsMzAiIHJvbT0iMzIzMSIgcnBoPSIxIiBydGM9IjIxNTQ5MCIgc2N0PSJCUiIgc2R0PSIyMDIxLTA5LTA5LTAzOjAwIiBzb3Q9IjU5Ni40MiIgc3N0PSJTUCIgc3d0PSI1OTYuNDIiIHN6aT0iOTU4MyIgdG1wPSIyMDIxLTA5LTA5VDEwOjQyOjM3LjgxOC0wMzowMCIvPg==  |
