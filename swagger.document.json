{"info": {"title": "sub-backend-hotels-store", "description": "BFF Stores", "version": "1.0", "license": {"name": "MIT"}}, "tags": [], "paths": {"/health": {"get": {"tags": ["Health"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": []}}, "/hotels/{id}/rooms": {"get": {"tags": ["Rooms"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": [{"name": "checkIn", "in": "query", "required": true}, {"name": "checkOut", "in": "query", "required": true}, {"name": "packageGroup", "in": "query", "required": true}, {"name": "rooms", "in": "query", "required": true}, {"name": "hotelId", "in": "query", "required": true}, {"name": "zoneId", "in": "query", "required": true}, {"name": "id", "in": "path", "required": true}, {"name": "gtw-sec-user-token", "in": "header", "required": true}]}}, "/hotels/{id}/rooms/{rateToken}": {"get": {"tags": ["hasAvail"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": [{"name": "id", "in": "path", "required": true}, {"name": "rateToken", "in": "path", "required": true}, {"name": "gtw-sec-user-token", "in": "header", "required": true}]}}, "/hotels/{id}": {"get": {"tags": ["Details"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": [{"name": "id", "in": "path", "required": true}, {"name": "gtw-sec-user-token", "in": "header", "required": true}]}}, "/hotels/booking/{source}/{locatorCode}": {"get": {"tags": ["importLocator"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": [{"name": "source", "in": "path", "required": true}, {"name": "locatorCode", "in": "path", "required": true}, {"name": "gtw-sec-user-token", "in": "header", "required": true}]}}, "/hotels/booking/rooms/{source}/{locatorCode}": {"get": {"tags": ["importLocator"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": [{"name": "source", "in": "path", "required": true}, {"name": "locatorCode", "in": "path", "required": true}, {"name": "gtw-sec-user-token", "in": "header", "required": true}]}}, "/hotels": {"get": {"tags": ["Avail"], "produces": ["application/json"], "responses": {"200": {"description": "Succeded", "schema": {"type": "object"}}}, "description": "", "parameters": [{"name": "checkIn", "in": "query", "required": true}, {"name": "checkOut", "in": "query", "required": true}, {"name": "packageGroup", "in": "query", "required": true}, {"name": "rooms", "in": "query", "required": true}, {"name": "zoneId", "in": "query", "required": true}, {"name": "gtw-sec-user-token", "in": "header", "required": true}]}}}, "swagger": "2.0", "securityDefinitions": {"api_key": {"type": "Bearer", "name": "access_token", "in": "authorization"}}, "produces": ["application/json"]}