import 'reflect-metadata';

import * as Bluebird from 'bluebird';
import consulConnection from 'consul';
import { injectable } from 'inversify';

@injectable()
export class ConsulClient {
	constructor() { }

	scheduleConfigurations() {

		try {

			const consul = consulConnection({ host: process.env.CONSUL_HOST, promisify: fromCallback });

			const keys = [
				{ key: "sub-backend-hotels-store/installment", setEnv: (value: string) => process.env["INSTALLMENT"] = value },
				{ key: "sub-backend-hotels-store/resorts", setEnv: (value: string) => process.env["RESORTS"] = value },
				{ key: "sub-backend-hotels-store/resortsInter", setEnv: (value: string) => process.env["RESORTS_INTER"] = value },
				{ key: "sub-backend-hotels-store/disneyHotels", setEnv: (value: string) => process.env["DISNEY_HOTELS"] = value },
				{ key: "sub-backend-hotels-store/url_opportunities", setEnv: (value: string) => process.env["URL_OPPORTUNITIES"] = value },
				{ key: "sub-backend-hotels-store/redis_host", setEnv: (value: string) => process.env["REDIS_HOST"] = value },
				//{ key: "sub-backend-hotels-store/redis_host", setEnv: (value: string) => "aaacnbgjnqaj4nlj3edoovakpkkgiw3xeimjhc4hv2sudpienvgnqvq-p.redis.sa-saopaulo-1.oci.oraclecloud.com"  }, // RODAR LOCAL PRODUÇÃO
				{ key: "sub-backend-hotels-store/redis_port", setEnv: (value: string) => process.env["REDIS_PORT"] = value }
			];

			const timeout = 5 * 60 * 1000;
			keys.map(key => ({ watch: consul.watch({ method: consul.kv.get, options: { ...key, timeout } }), key }))
				.map(({ watch, key }) => {

					watch.on('change', function (data: any, _: any) {
						if (data && data.Value) {
							key.setEnv(data.Value);
							console.log(`key: ${data.Key} - value: ${data.Value}`)
						}
					});

					watch.on('error', function (error) {
						console.error(error);
					});

				});
		}
		catch (error) {
			console.error(error);
		}
	}
}

const fromCallback = (fn) => {
	return new Bluebird(function (resolve, reject) {
		try {
			return fn(function (err, data, res) {
				if (err) {
					err.res = res;
					return reject(err);
				}

				return resolve([data, res]);
			});
		} catch (err) {
			return reject(err);
		}
	});
}