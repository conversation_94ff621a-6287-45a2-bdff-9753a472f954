{"include": ["./src/**/*"], "compilerOptions": {"target": "es2017", "lib": ["es6", "dom"], "types": ["reflect-metadata", "node"], "module": "commonjs", "moduleResolution": "node", "noImplicitAny": false, "sourceMap": true, "outDir": "./dist", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types", "./typings"]}}