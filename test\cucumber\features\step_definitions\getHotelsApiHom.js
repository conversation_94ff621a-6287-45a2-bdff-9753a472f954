const pactum = require('pactum');
const { Given, When, Then, Before } = require('@cucumber/cucumber');

let spec = pactum.spec();

Before(() => { spec = pactum.spec(); });

Given('I make a GET request api hom hotels with {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}, {string}', function (idHotel, checkIn, checkOut, itemsPerPage, packageGroup, rooms, zoneId, pageNumber, sortBy, sortOrder, maxPrice, minPrice, name) {
  spec.get("https://apihom.services.cvc.com.br/sub-backend-hotels-store/hotels?checkIn=" + checkIn + "&checkOut=" + checkOut + "&itemsPerPage=" + itemsPerPage + "&packageGroup=" + packageGroup + "&rooms=" + rooms + "&zoneId=" + zoneId + "&pageNumber=" + pageNumber + "&sortBy=" + sortBy + "&sortOrder=" + sortOrder + "&maxPrice" + maxPrice + "&minPrice" + minPrice + "&name" + name);
  spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************.7sqMZfZU-JN_NFNfnEjOjoF82zGh9GfTmyFzGKz-2GA');
  spec.withHeaders('gtw-transaction-id', '75f81459-f8b9-1049-f010-a4ee7033982a-1640869036741');
});

When('I receive a response api hom hotels', async function () {
  await spec.toss();
});


Then('response api hom hotels should have a status {int}', async function (code) {
  spec.response().should.have.status(code);
});