import moment from "moment";

const isInvalidDate = (checkIn, checkOut) => {
  try {

    if (!checkIn || !checkOut) return false;

    const currentDate = moment().format("YYYY-MM-DD");
    const checkin = moment(checkIn).format("YYYY-MM-DD");
    const checkout = moment(checkOut).format("YYYY-MM-DD");

    const isDate = !moment(checkin).isValid() || !moment(checkout).isValid() || moment(checkin).isAfter(checkout);
    const isAfterDate = moment(checkin).isBefore(currentDate)|| moment(currentDate).isAfter(checkin);
    const isInvalid = isAfterDate || isDate;
    return isInvalid;
  } catch (err) {
    return false;
  }
};

export default isInvalidDate;
