const pactum = require('pactum');
const { Given, When, Then, Before } = require('@cucumber/cucumber');

let spec = pactum.spec();

Before(() => { spec = pactum.spec(); });

Given('I make a GET request rooms hasAvail api hom with {string}, {string}', function (idHotel, token) {
  spec.get("https://apihom.services.cvc.com.br/sub-backend-hotels-store/hotels/" + idHotel + "/rooms/" + token");
  spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.eSI7adU620BGiO2X8C3uVxkEvHdzhIJmyIuyegvB8ME');
  spec.withHeaders('gtw-transaction-id', 'b74a9265-fdd0-297a-8527-c3d57fb16b95-1631194638376');
});

When('I receive a response rooms hasAvail api hom', async function () {
  await spec.toss();
});

Then('response rooms hasAvail api hom should have a status {int}', async function (code) {
  spec.response().should.have.status(code);
});
