image: node:13.14.0

stages:
  - build
  - test

build_application:
  stage: build
  tags:
    - docker
  script:
    - echo "Install Dependencies"
    - npm ci
    - echo "Build Application"
    - npm run build
  only:
    - merge_requests

unit_testing:
  stage: test
  tags:
    - docker
  script:
    - echo "Install Dependencies"
    - npm ci
    - echo "Unit Testing"
    - npm run test
  only:
    - merge_requests
