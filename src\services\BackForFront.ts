import * as jose from "jose";
import FilteringAndOrdering from "../FilteringAndOrdering";
import MetaBuilder from "../MetaBuilder";
import { Consul } from "./Consul";
import Request from "./Request";

import {
  base64Decode,
  digestMD5UponPertinentFields,
  fetchFromJWT,
  filterHeaders,
  getBrokers,
  getEnv,
  injectValidateOne,
  shouldTargetNewGateway,
} from "../utils";

import isInvalidDate from "../utils/isInvalidDate";

import Paging from "../Paging";
import Log from "../config/log";
import logger from "../config/logger";
import { StopWatch } from "../config/stopwatch";

export default class BackForFront {
  private path: string;
  private basePath: string;
  private baseURL: string;
  private headers;
  private agentSignAndBranchId = [];
  private log = new Log();
  private newGatewayUrl = getEnv("HOTEL_BACK_FOR_FRONT_GATEWAY_URL");
  private newGatewayBasePath = "v1/back-for-front/hotels";
  private OSBUrl = getEnv("HOTEL_BACK_FOR_FRONT_OSB_URL");
  private static gtwSecUserTokenKey = getEnv("HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN");
  private OSBBasePath = "hotels";
  private OSB = "OSB";
  private target = this.OSB;
  private socketID: string = null;
  private brokers = getBrokers();
  private websocketURL;
  private theseBrokersShouldAimOSB = ["CVC"];
  private queryValidateOne;
  public noWebsocket: boolean = false;
  public keySearch: string = null;
  private hasDeduplicate: boolean;

  constructor(consul: Consul, req, path = "", importLoc: boolean = false) {
    this.baseURL = this.OSBUrl;
    this.basePath = this.OSBBasePath;
    this.headers = filterHeaders(req.headers);
    this.queryValidateOne = req.query.validate;
    this.hasDeduplicate = this.headers["gtw-deduplicate"] === "true";

    this.path = path;
    this.#extractAgentSignAndBranchId();
    this.#addAgentSignAndBranchIdToHeaders();
    this.getWebsocketIDAndURL();

    const _shouldTargetNewGateway = shouldTargetNewGateway(consul, this.agentSignAndBranchId[1], req, importLoc);

    if (_shouldTargetNewGateway === true) {
      this.baseURL = this.newGatewayUrl;
      this.basePath = this.newGatewayBasePath;
      this.target = "NEW :) Gateway";
    }

    if (importLoc) {
      const gtwSecUserToken = req.headers["gtw-sec-user-token"];
      if (gtwSecUserToken) {
        const tokenDecoded = jose.decodeJwt(gtwSecUserToken) as any;
        const user = tokenDecoded?.credential?.user;
        if (user) {
          this.headers["Gtw-Username"] = user;
        }
      }
    }

    const noWebsocket: boolean = req.headers["no-websocket"] === "true" ? true : false;
    this.noWebsocket = noWebsocket;
    this.keySearch = digestMD5UponPertinentFields({ query: req.query }, this);
    console.log("this.keySearch", this.keySearch);
  }

  public pretendThisIsOSB(andRun) {
    if (this.target === this.OSB) {
      andRun();
      return;
    }
    const save = [this.target, this.baseURL, this.basePath];
    this.target = this.OSB;
    this.baseURL = this.OSBUrl;
    this.basePath = this.OSBBasePath;
    andRun();
    this.target = save[0];
    this.baseURL = save[1];
    this.basePath = save[2];
  }

  private getWebsocketIDAndURL() {
    const websocketKeyName = getEnv("HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY");
    const websocketId = this.headers[websocketKeyName];
    if (!!websocketId === false) {
      return;
    }

    let build: any = base64Decode(websocketId);
    if (build === null) {
      return;
    }
    build = build.split(/\|/);
    if (build.length < 2) {
      this.log.error(`Could not acquire websocketID from ${build}`);
    }
    this.socketID = build[1];
    this.websocketURL = build[0];
    this.log.debug(
      [
        `Acquired socketID: ${this.socketID} and websocket server address:`,
        `${this.websocketURL} from ${websocketId}`,
      ].join(" ")
    );
  }

  private forceBrokerToOSB(broker) {
    for (const each of this.theseBrokersShouldAimOSB) {
      if (each.toLowerCase() === broker.toLowerCase()) {
        this.log.debug(`Exceptionally for this ${broker} broker, we are aiming for OSB`);
        return true;
      }
    }
    return false;
  }

  #extractAgentSignAndBranchId() {
    this.agentSignAndBranchId = fetchFromJWT(
      this.headers,
      BackForFront.gtwSecUserTokenKey,
      ["credential.agentSign", "credential.branchId"],
      "we can use it though more than one context"
    );
  }

  getAgentSignAndBranchId() {
    return this.agentSignAndBranchId;
  }

  #addAgentSignAndBranchIdToHeaders() {
    if (this.agentSignAndBranchId.length < 2) {
      this.log.error(
        [
          "agentSign and branchId could not have been acquired earlier,",
          "as we are aiming to OSB, they must be present",
        ].join(" ")
      );
      return;
    }

    var branchId = this.agentSignAndBranchId[1];

    // if(branchId == "1020"|| branchId == 1020 ){
    //   delete this.headers["gtw-pricing"]; // Profit Split
    // }

    this.headers["Gtw-Agent-Sign"] = this.agentSignAndBranchId[0];
    this.headers["Gtw-Branch-Id"] = branchId;
  }

  post(data) {
    return new Request(this.baseURL).post({
      headers: this.headers,
      data: data,
      url: `${this.basePath}/${this.path}`,
    });
  }

  get(params) {
    this.log.debug(params, `HTTP GET: ${this.baseURL}/${this.basePath}/${this.path}`);

    const {
      checkIn,
      checkOut,
      eventCode,
      itemsPerPage,
      packageGroup,
      pageNumber,
      preferences,
      rooms,
      sortBy,
      sortOrder,
      validate,
      zoneId,
    } = params;

    const curl = `curl --location --request GET '${this.baseURL}/${this.basePath}/${this.path}?checkIn=${checkIn}&checkOut=${checkOut}&eventCode=${eventCode}&itemsPerPage=${itemsPerPage}&packageGroup=${packageGroup}&pageNumber=${pageNumber}&preferences=${preferences}&rooms=${rooms}&sortBy=${sortBy}&sortOrder=${sortOrder}&validate=${validate}&zoneId=${zoneId}' \
		--header 'accept: application/json, text/javascript, */*; q=0.01' \
		--header 'gtw-sec-user-token: ${this.headers["gtw-sec-user-token"]}' \
		--header 'Gtw-Agent-Sign: ${this.headers["Gtw-Agent-Sign"]}' \
		--header 'Gtw-Branch-Id: ${this.headers["Gtw-Branch-Id"]}' \
		--header 'gtw-transaction-id: ${this.headers["gtw-transaction-id"]}' \
		--header 'Gtw-Username: ${this.headers["Gtw-Username"]}' \
		--header 'gtw-deduplicate: ${this.headers["gtw-deduplicate"]}' \
		--header 'gtw-pricing: ${this.headers["gtw-pricing"]}'`;

    console.log("====================================================");
    console.log(curl);
    console.log("====================================================");
    //console.log("Headers", JSON.stringify(this.headers));
    //console.log("#########################################################");

    return new Request(this.baseURL).get({
      headers: this.headers,
      params: params,
      url: `${this.basePath}/${this.path}`,
    });
  }

  async getAvailByZoneId(params) {
    const { checkIn, checkOut, packageGroup, rooms, zoneId, ids } = params;
    let url = `${this.basePath}/${this.path}?checkIn=${checkIn}&checkOut=${checkOut}&packageGroup=${packageGroup}&rooms=${rooms}&zoneId=${zoneId}&ids=${ids}`;

    console.log("##############################");

    const curl = `curl --location --request GET '${this.baseURL}/${url}' \
		--header 'accept: application/json, text/javascript, */*; q=0.01' \
		--header 'Gtw-Agent-Sign: ${this.headers["Gtw-Agent-Sign"]}' \
		--header 'Gtw-Branch-Id: ${this.headers["Gtw-Branch-Id"]}' \
		--header 'gtw-transaction-id: ${this.headers["gtw-transaction-id"]}' \
		--header 'gtw-deduplicate: ${this.headers["gtw-deduplicate"]}' \
		--header 'gtw-pricing: ${this.headers["gtw-pricing"]}'`;

    console.log(curl);
    console.log("##############################");
    //console.log("Headers", JSON.stringify(this.headers));
    //console.log("#########################################################");

    return new Request(this.baseURL).get({ headers: this.headers, url });
  }

  async getAvailBySource(params, source: string) {
    const transactionId = this.headers["gtw-transaction-id"];
    const { checkIn, checkOut, packageGroup, rooms, sortBy, sortOrder, zoneId, eventCode, unavailable } = params;
    let url = `${this.basePath}/${this.path}?checkIn=${checkIn}&checkOut=${checkOut}&packageGroup=${packageGroup}&rooms=${rooms}&sortBy=${sortBy}&sortOrder=${sortOrder}&zoneId=${zoneId}&transactionId=${transactionId}`;
    if (unavailable) url += `&unavailable=${unavailable}`;
    if (eventCode) url += `&eventCode=${eventCode}`;
    if (!this.hasDeduplicate) url += `&source=${source}`;

    delete this.headers["cookie"];

    // var branchId = this.headers["Gtw-Branch-Id"];
    // if(branchId == "1020"|| branchId == 1020 ){
    //   delete this.headers["gtw-pricing"];
    // }

    // const curl = `curl --location --request GET '${this.baseURL}/${url}' \
    // --header 'accept: application/json, text/javascript, */*; q=0.01' \
    // --header 'gtw-sec-user-token: ${this.headers["gtw-sec-user-token"]}' \
    // --header 'Gtw-Agent-Sign: ${this.headers["Gtw-Agent-Sign"]}' \
    // --header 'Gtw-Branch-Id: ${this.headers["Gtw-Branch-Id"]}' \
    // --header 'gtw-transaction-id: ${this.headers["gtw-transaction-id"]}'`;

    // console.log("====================================================");
    // console.log(curl);
    // console.log("====================================================");

    this.printCurlCommand(this.headers, `${this.baseURL}/${url}`);

    return new Request(this.baseURL).get({ headers: this.headers, url });
  }

  printCurlCommand(headers, url) {
    try {
      const curlHeaders = Object.entries(headers)
        .map(([key, value]) => {
          // Garante que o value seja uma string.
          const stringValue = String(value);
          // Escapa as aspas duplas no value.
          return `-H "${key}: ${stringValue.replace(/"/g, '\\"')}" \\`;
        })
        .join("");
      //}).join("\n");

      const separadorInicio = "====================== CURL ======================";
      const separadorFim = "====================== FIM ======================";

      const curl = `${separadorInicio}\n` + `curl -X GET "${url}" \\\n` + `${curlHeaders}\n` + `${separadorFim}`;

      console.log(curl);
      console.log("#########################################################");
      //console.log("Headers", JSON.stringify(headers));
    } catch (error) {
      console.error("Erro ao montar o comando cURL:", error);
    }
  }

  async getDetail(hotelId: string) {
    const transactionId = this.headers["gtw-transaction-id"];
    const agentSign = this.agentSignAndBranchId[0];
    const branchId = this.agentSignAndBranchId[1];
    const operation = "GATEWAY:DETAIL";
    const url = `${this.basePath}/${this.path}?transactionId=${transactionId}`;

    const timer = new StopWatch();
    timer.start();

    let duration = timer.getElapsedMilliseconds();

    let objLogger: any = {
      operation,
      transactionId,
      duration,
      hotelId,
      url,
      baseURL: this.baseURL,
      agentSign,
      branchId,
      short_message: "info",
    };

    try {
      logger.info(objLogger, `Iniciando consulta de detail no gateway de hoteis`);

      console.log("############### REQUEST DETAIL ##########################");

      const curl = `curl --location --request GET '${this.baseURL}/${url}' \
	  --header 'accept: application/json, text/javascript, */*; q=0.01' \
	  --header 'gtw-sec-user-token: ${this.headers["gtw-sec-user-token"]}' \
	  --header 'Gtw-Agent-Sign: ${this.headers["Gtw-Agent-Sign"]}' \
	  --header 'Gtw-Branch-Id: ${this.headers["Gtw-Branch-Id"]}' \
	  --header 'gtw-transaction-id: ${this.headers["gtw-transaction-id"]}' \
	  --header 'gtw-deduplicate: ${this.headers["gtw-deduplicate"]}' \
	  --header 'gtw-pricing: ${this.headers["gtw-pricing"]}'`;

      console.log(curl);
      console.log("#########################################################");
      //console.log("Headers", JSON.stringify(this.headers));
      //console.log("#########################################################");

      const response = await new Request(this.baseURL).get({ headers: this.headers, url });

      duration = timer.getElapsedMilliseconds();
      const short_message = "success";
      objLogger = { ...objLogger, duration, short_message };
      logger.info(objLogger, `Consulta consulta de detail no gateway de hoteis concluida com sucesso`);

      return response;
    } catch (err) {
      duration = timer.getElapsedMilliseconds();
      const short_message = "error";
      const { message } = err;
      objLogger = { ...objLogger, duration, short_message, message };
      logger.error(objLogger, `Consulta consulta de detail no gateway de hoteis concluida com erro`);

      throw err;
    }
  }

  async getRooms(params) {
    const transactionId = this.headers["gtw-transaction-id"];
    const agentSign = this.agentSignAndBranchId[0];
    const branchId = this.agentSignAndBranchId[1];
    const operation = "GATEWAY:ROOMS";
    const { checkIn, checkOut, hotelId, packageGroup, rooms, zoneId, packageService } = params;

    let url = `${this.basePath}/${this.path}?checkIn=${checkIn}&checkOut=${checkOut}&hotelId=${hotelId}&packageGroup=${packageGroup}&rooms=${rooms}&zoneId=${zoneId}&transactionId=${transactionId}`;
    if (packageService) url += "&packageService=" + packageService;

    this.validDates(checkIn, checkOut);

    const timer = new StopWatch();
    timer.start();

    let duration = timer.getElapsedMilliseconds();

    let objLogger: any = {
      operation,
      checkIn,
      checkOut,
      transactionId,
      duration,
      hotelId,
      packageGroup,
      rooms,
      zoneId,
      url,
      baseURL: this.baseURL,
      agentSign,
      branchId,
      short_message: "info",
    };

    try {
      logger.info(objLogger, `Iniciando consulta de rooms no gateway de hoteis`);

      //   console.log("############### REQUEST ROOMS ##########################");
      //   console.log("baseURL", this.baseURL);
      //   console.log("url", url);
      //   console.log("headers", JSON.stringify(this.headers));
      //   console.log("#########################################################");

      delete this.headers["ratetoken"];
      delete this.headers["cookie"];

      console.log("############### REQUEST ROOMS ##########################");

      const curl = `curl --location --request GET '${this.baseURL}/${url}' \
	  --header 'accept: application/json, text/javascript, */*; q=0.01' \
	  --header 'gtw-sec-user-token: ${this.headers["gtw-sec-user-token"]}' \
	  --header 'Gtw-Agent-Sign: ${this.headers["Gtw-Agent-Sign"]}' \
	  --header 'Gtw-Branch-Id: ${this.headers["Gtw-Branch-Id"]}' \
	  --header 'gtw-transaction-id: ${this.headers["gtw-transaction-id"]}' \
	  --header 'gtw-deduplicate: ${this.headers["gtw-deduplicate"]}' \
	  --header 'gtw-pricing: ${this.headers["gtw-pricing"]}'`;

      console.log(curl);
      console.log("#########################################################");
      //console.log("Headers", JSON.stringify(this.headers));
      //console.log("#########################################################");

      const response = await new Request(this.baseURL).get({ headers: this.headers, url });

      duration = timer.getElapsedMilliseconds();
      const short_message = "success";
      objLogger = { ...objLogger, duration, short_message };
      logger.info(objLogger, `Consulta consulta de rooms no gateway de hoteis concluida com sucesso`);

      return response;
    } catch (err) {
      duration = timer.getElapsedMilliseconds();
      const short_message = "error";
      const { message } = err;
      const errorCode = err?.response?.status;

      try {
        const { response } = err;
        const { data } = response;
        if (data.room && data.room[0]) {
          const room = data.room[0];
          if (
            (room.error && room.error.code && room.error.code.trim() === "NO_AVAIL_FOUND") ||
            (room.error.description && room.error.description.trim() === "No availability was found") ||
            room.error.description.trim() === "SERVICE ERROR: hotel-gateway-broker; STATUS: 400"
          ) {
            return { data: { rooms: [] } };
          }
        }
      } catch (err) {}
      objLogger = { ...objLogger, duration, short_message, message, status: errorCode || "unknown" };
      logger.error(objLogger, `Consulta consulta de rooms no gateway de hoteis concluida com erro`);

      console.log(objLogger);

      throw err;
    }
  }

  async getHasAvail(hotelId, ignorePriceChange, packageService) {
    const transactionId = this.headers["gtw-transaction-id"];
    const agentSign = this.agentSignAndBranchId[0];
    const branchId = this.agentSignAndBranchId[1];
    const operation = "GATEWAY:HASAVAIL";

    let url = `${this.basePath}/${this.path}?1=1`;

    if (ignorePriceChange) url += `&ignorePriceChange=${ignorePriceChange}`;
    if (packageService) url += `&packageService=${packageService}`;

    const timer = new StopWatch();
    timer.start();

    let duration = timer.getElapsedMilliseconds();

    let objLogger: any = {
      operation,
      transactionId,
      duration,
      hotelId,
      url,
      baseURL: this.baseURL,
      agentSign,
      branchId,
      short_message: "info",
    };

    try {
      logger.info(objLogger, `Iniciando consulta de hasavail no gateway de hoteis`);

      console.log("############### REQUEST HASAVAIL ##########################");

      delete this.headers["ratetoken"];
      delete this.headers["cookie"];

      const curl = `curl --location --request GET '${this.baseURL}/${url}' \
	  --header 'accept: application/json, text/javascript, */*; q=0.01' \
	  --header 'gtw-sec-user-token: ${this.headers["gtw-sec-user-token"]}' \
	  --header 'Gtw-Agent-Sign: ${this.headers["Gtw-Agent-Sign"]}' \
	  --header 'Gtw-Branch-Id: ${this.headers["Gtw-Branch-Id"]}' \
	  --header 'gtw-transaction-id: ${this.headers["gtw-transaction-id"]}' \
	  --header 'gtw-deduplicate: ${this.headers["gtw-deduplicate"]}' \
	  --header 'gtw-pricing: ${this.headers["gtw-pricing"]}'`;

      console.log(curl);
      console.log("#########################################################");
      //console.log("Headers", JSON.stringify(this.headers));
      //console.log("#########################################################");

      const response = await new Request(this.baseURL).get({ headers: this.headers, url });
      // const data = {
      // 	"room": {
      // 			"error": {
      // 					"message": "O hotel não está mais disponível no momento, para o período pesquisado.",
      // 					"code": "406"
      // 			}
      // 	}
      // }
      // const response = { data };
      const errorMessage = response?.data?.room?.error?.message;

      duration = timer.getElapsedMilliseconds();
      const short_message = "success";
      objLogger = { ...objLogger, duration, short_message, message: errorMessage ? errorMessage : "SUCCESS" };
      logger.info(objLogger, `Consulta consulta de hasavail no gateway de hoteis concluida com sucesso`);

      if (errorMessage) return { message: errorMessage };

      return response;
    } catch (err) {
      const errorMessage = err?.response?.data?.message;
      const errorCode = err?.response?.status;

      duration = timer.getElapsedMilliseconds();
      const short_message = "error";
      const { message } = err;
      objLogger = {
        ...objLogger,
        duration,
        short_message,
        message: errorMessage ? errorMessage : message,
        status: errorCode || "unknown",
      };
      logger.error(objLogger, `Consulta consulta de hasavail no gateway de hoteis concluida com erro`);

      console.log(objLogger);

      if (errorMessage) return { message: errorMessage };

      throw err;
    }
  }

  private requestToWebSocket(filteredAndOrdered, promisePack, meta) {
    const request = new Request(this.websocketURL);
    request
      .post({
        headers: {
          "Content-Type": "application/json",
          "Gtw-Sec-Websocket-Key": this.socketID,
        },
        url: "socket",
        data: {
          hotels: filteredAndOrdered,
          meta: meta,
        },
      })
      .then((socketResponse) => {
        this.log.debug(`Websocket server response after sending ${promisePack.broker}: ${socketResponse.data}`);
      })
      .catch((error) => {
        this.log.error(`Could NOT reach websocket server, ${error.toString()}`);
      });
  }

  private accountEvenIfTheUnexpectedHappened(
    metaBuilder,
    hotelsCollection,
    promisePack,
    countResponses,
    brokersLength
  ) {
    // Verifica se é um fluxo de disney/resorts (tem consulHotel) ou fluxo normal (tem broker)
    if (promisePack.consulHotel) {
      // Para fluxo de disney/resorts, mantém o comportamento original
      if (countResponses >= brokersLength) {
        metaBuilder.executed = brokersLength;
        const meta = metaBuilder.getMeta();
        injectValidateOne({ validate: this.queryValidateOne }, hotelsCollection);
        this.requestToWebSocket(hotelsCollection, promisePack, meta);
      }
    } else {
      // Para fluxo normal, aplica a lógica de deduplicate
      if (this.hasDeduplicate ? countResponses >= 1 : countResponses >= brokersLength) {
        metaBuilder.executed = this.hasDeduplicate ? 1 : brokersLength;
        const meta = metaBuilder.getMeta();
        injectValidateOne({ validate: this.queryValidateOne }, hotelsCollection);
        this.requestToWebSocket(hotelsCollection, promisePack, meta);
      }
    }
  }

  validDates(checkIn, checkOut) {
    if (isInvalidDate(checkIn, checkOut)) {
      this.log.error(`CheckIn and checkOut with retroactive dates`);
      throw new Error("CheckIn and checkOut with retroactive dates");
    }
  }

  private requestToCache(filteredAndOrdered, redis, meta) {
    const data = {
      hotels: filteredAndOrdered,
      meta: meta,
    };
    redis.set(this.keySearch, JSON.stringify(data));
  }

  reachToWebsocketServer(query, method, redis) {
    if (!!this.socketID === false && this.noWebsocket === false) {
      this.log.error(`SocketID is not defined, stopping`);
      return;
    }

    const { checkIn, checkOut, packageGroup, rooms, sortBy, sortOrder, zoneId, eventCode, unavailable } = query;

    let brokers = eventCode ? this.theseBrokersShouldAimOSB : this.brokers;

    const promisesPack = [];
    const responses = { hotels: [] };
    const allHotels = responses.hotels;
    let filteredAndOrdered = allHotels;
    let filteredAndOrderedPaging = allHotels;
    let meta = {};
    let metaBuilder = new MetaBuilder(this.hasDeduplicate ? 1 : brokers.length);
    let countResponses = 0;

    const transactionId = this.headers["gtw-transaction-id"];
    const agentSign = this.agentSignAndBranchId[0];
    const branchId = this.agentSignAndBranchId[1];
    const operation = "GATEWAY:AVAIL";

    let url = `${this.basePath}/${this.path}?checkIn=${checkIn}&checkOut=${checkOut}&packageGroup=${packageGroup}&rooms=${rooms}&sortBy=${sortBy}&sortOrder=${sortOrder}&zoneId=${zoneId}&&transactionId=${transactionId}`;
    if (unavailable) url += `&unavailable=${unavailable}`;
    if (eventCode) url += `&eventCode=${eventCode}`;

    const timer = new StopWatch();
    timer.start();

    let duration = timer.getElapsedMilliseconds();

    let objLogger: any = {
      operation,
      transactionId,
      duration,
      url,
      baseURL: this.baseURL,
      agentSign,
      branchId,
      short_message: "info",
    };

    const callBroker = async (query, broker) => {
      // Se o header gtw-deduplicate for true, não adiciona o source na URL
      url = this.hasDeduplicate ? url : url + `&souce=${broker}`;
      objLogger = { ...objLogger, url };
      const logMessage = this.hasDeduplicate
        ? "Iniciando consulta de avail no gateway de hoteis sem source (deduplicate ativado)"
        : `Iniciando consulta de avail no gateway de hoteis para o source ${broker}`;
      logger.info(objLogger, logMessage);

      return this.getAvailBySource({ ...query }, broker);
    };

    // Se o header gtw-deduplicate for true, faz apenas uma chamada
    if (this.hasDeduplicate) {
      const broker = "TODOS"; // Usa um valor padrão para indicar que está com deduplicate
      if (this.forceBrokerToOSB(broker) === false) {
        const cb = { promise: callBroker(query, broker), broker: broker };
        promisesPack.push(cb);
      } else {
        this.pretendThisIsOSB(() => {
          const cb = { promise: callBroker(query, broker), broker: broker };
          promisesPack.push(cb);
        });
      }
    } else {
      // Se não for true ou não existir, mantém o comportamento original
      brokers.forEach((broker) => {
        if (this.forceBrokerToOSB(broker) === false) {
          const cb = { promise: callBroker(query, broker), broker: broker };
          promisesPack.push(cb);
        } else {
          this.pretendThisIsOSB(() => {
            const cb = { promise: callBroker(query, broker), broker: broker };
            promisesPack.push(cb);
          });
        }
      });
    }

    promisesPack.forEach((promisePack) => {
      promisePack.promise
        .then((response) => {
          const incoming = response.data;
          const hotelsTotal = incoming?.hotels && incoming.hotels.length > 0 ? incoming.hotels.length : 0;

          duration = timer.getElapsedMilliseconds();
          const short_message = "success";
          objLogger = { ...objLogger, duration, short_message };
          logger.info(
            objLogger,
            `Consulta de avail no gateway de hoteis concluida com sucesso para o source ${promisePack.broker} - total (${hotelsTotal})`
          );

          countResponses++;

          if (incoming?.hotels && incoming.hotels.length > 0) {
            incoming.hotels.forEach((hotel) => {
              allHotels.push(hotel);
            });

            filteredAndOrdered = new FilteringAndOrdering(query, allHotels).getFilteredAndOrdered();
            metaBuilder = new MetaBuilder(this.hasDeduplicate ? 1 : brokers.length);
            metaBuilder.executed = countResponses;
            metaBuilder.comprise(filteredAndOrdered);
            meta = metaBuilder.getMeta();
            filteredAndOrderedPaging = new Paging(query, filteredAndOrdered).getPagingResults();
          } else {
            this.log.debug(`NO hotels were returned from ${promisePack.broker}`);
            this.accountEvenIfTheUnexpectedHappened(
              metaBuilder,
              filteredAndOrderedPaging,
              promisePack,
              countResponses,
              brokers.length
            );
            return;
          }

          this.makeAnStatementAboutWhatsUp(incoming, filteredAndOrderedPaging, promisePack, allHotels);
          injectValidateOne({ validate: this.queryValidateOne }, filteredAndOrderedPaging);

          if (this.noWebsocket) {
            this.requestToCache(filteredAndOrderedPaging, redis, meta);
          } else {
            this.requestToWebSocket(filteredAndOrderedPaging, promisePack, meta);

            try {
              const digestMD5 = digestMD5UponPertinentFields({ query: query }, this);
              redis.set(digestMD5, JSON.stringify(responses));
            } catch (error) {
              this.log.error("Could not save to Redis");
            }
          }
        })
        .catch((error) => {
          duration = timer.getElapsedMilliseconds();
          const short_message = "error";
          objLogger = { ...objLogger, duration, short_message };
          logger.error(
            objLogger,
            `Consulta de avail no gateway de hoteis concluida com erro para o source ${promisePack.broker}`
          );

          countResponses++;
          this.accountEvenIfTheUnexpectedHappened(
            metaBuilder,
            filteredAndOrderedPaging,
            promisePack,
            countResponses,
            brokers.length
          );

          this.log.error(error.toString(), "Reaching to websocket server");
        });
    });
  }

  reachWebSocketHotelConsul(query, redis) {
    if (!!this.socketID === false && this.noWebsocket === false) {
      this.log.error(`SocketID is not defined, stopping`);
      return;
    }

    const { checkIn, checkOut, packageGroup, rooms, sortBy, sortOrder } = query;

    let consulHotels = [];
    try {
      if (query.resorts) {
        if (query.inter === "true") {
          // sub-backend-hotels-store/resortsInter
          consulHotels = JSON.parse(process.env.RESORTS_INTER);
        } else {
          // sub-backend-hotels-store/resorts/
          consulHotels = JSON.parse(process.env.RESORTS);
        }
      } else if (query.disney) {
        // sub-backend-hotels-store/disneyHotels
        consulHotels = JSON.parse(process.env.DISNEY_HOTELS);
      }
    } catch (err) {}

    const promisesPack = [];
    const responses = { hotels: [] };
    const allHotels = responses.hotels;
    let filteredAndOrdered = allHotels;
    let filteredAndOrderedPaging = allHotels;
    let meta = {};
    let metaBuilder = new MetaBuilder(consulHotels.length);
    let countResponses = 0;

    const transactionId = this.headers["gtw-transaction-id"];
    const agentSign = this.agentSignAndBranchId[0];
    const branchId = this.agentSignAndBranchId[1];
    const operation = "GATEWAY:AVAIL";

    let url = `${this.basePath}/${this.path}?checkIn=${checkIn}&checkOut=${checkOut}&packageGroup=${packageGroup}&rooms=${rooms}&transactionId=${transactionId}`;

    const timer = new StopWatch();
    timer.start();

    let duration = timer.getElapsedMilliseconds();

    let objLogger: any = {
      operation,
      transactionId,
      duration,
      url,
      baseURL: this.baseURL,
      agentSign,
      branchId,
      short_message: "info",
    };

    const callConsulHotel = async (query, consulHotel) => {
      url = url + `&zoneId=${consulHotel.zoneId}&ids=${consulHotel.ids}`;
      objLogger = { ...objLogger, url };
      logger.info(objLogger, `Iniciando consulta de avail no gateway de hoteis para o zoneId ${consulHotel.zoneId}`);

      query["ids"] = consulHotel.ids;
      query["zoneId"] = consulHotel.zoneId;
      return this.getAvailByZoneId({ ...query });
    };

    consulHotels.forEach((consulHotel) => {
      const cb = { promise: callConsulHotel(query, consulHotel), consulHotel: consulHotel };
      promisesPack.push(cb);
    });

    promisesPack.forEach((promisePack) => {
      const { consulHotel } = promisePack;
      promisePack.promise
        .then((response) => {
          const incoming = response.data;
          const hotelsTotal = incoming?.hotels && incoming.hotels.length > 0 ? incoming.hotels.length : 0;

          duration = timer.getElapsedMilliseconds();
          const short_message = "success";
          objLogger = { ...objLogger, duration, short_message };
          logger.info(
            objLogger,
            `Consulta de avail no gateway de hoteis concluida com sucesso para o source ${promisePack.consulHotel.zoneId} - total (${hotelsTotal})`
          );

          countResponses++;

          if (incoming?.hotels && incoming.hotels.length > 0) {
            incoming.hotels.forEach((hotel) => {
              // O nome ficou resort para manter a compatibilidade com o front
              Object.assign(hotel, { resort: consulHotel });
              allHotels.push(hotel);
            });

            filteredAndOrdered = new FilteringAndOrdering(query, allHotels).getFilteredAndOrdered();
            metaBuilder = new MetaBuilder(consulHotels.length);
            metaBuilder.executed = countResponses;
            metaBuilder.comprise(filteredAndOrdered);
            meta = metaBuilder.getMeta();
            filteredAndOrderedPaging = new Paging(query, filteredAndOrdered).getPagingResults();
          } else {
            this.log.debug(`No hotels were returned from ${promisePack.consulHotel.zoneId}`);
            this.accountEvenIfTheUnexpectedHappened(
              metaBuilder,
              filteredAndOrderedPaging,
              promisePack,
              countResponses,
              consulHotels.length
            );
            return;
          }

          this.makeAnStatementAboutWhatsUp(incoming, filteredAndOrderedPaging, promisePack, allHotels);
          injectValidateOne({ validate: this.queryValidateOne }, filteredAndOrderedPaging);

          if (this.noWebsocket) {
            this.requestToCache(filteredAndOrderedPaging, redis, meta);
          } else {
            this.requestToWebSocket(filteredAndOrderedPaging, promisePack, meta);

            try {
              const digestMD5 = digestMD5UponPertinentFields({ query: query }, this);
              redis.set(digestMD5, JSON.stringify(responses));
            } catch (error) {
              this.log.error("Could not save to Redis");
            }
          }
        })
        .catch((error) => {
          duration = timer.getElapsedMilliseconds();
          const short_message = "error";
          objLogger = { ...objLogger, duration, short_message };
          logger.error(
            objLogger,
            `Consulta de avail no gateway de hoteis concluida com erro para o source ${promisePack.broker}`
          );

          countResponses++;
          this.accountEvenIfTheUnexpectedHappened(
            metaBuilder,
            filteredAndOrderedPaging,
            promisePack,
            countResponses,
            consulHotels.length
          );

          this.log.error(error.toString(), "Reaching to websocket server");
        });
    });
  }

  private makeAnStatementAboutWhatsUp(incoming, filteredAndOrdered, promisePack, allHotels) {
    this.log.debug(
      [
        `Response from ${this.target} arrived from ${promisePack.broker}`,
        `with ${incoming.hotels.length} hotels, summing ${allHotels.length}`,
        "with previously received and filtered hotels from other brokers,",
        `after applying filters ${filteredAndOrdered.length} hotels remained,`,
        `sending it to WebsocketServer:${this.socketID} filtered, sorted and "metaed",`,
        "and adding it to Redis without sorting nor filtering",
      ].join(" ")
    );
  }

  setLog(log) {
    this.log = log;
  }
}
