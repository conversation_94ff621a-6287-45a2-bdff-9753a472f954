const pactum = require('pactum');
const { Given, When, Then, Before } = require('@cucumber/cucumber');

let spec = pactum.spec();

Before(() => { spec = pactum.spec(); });

Given('I make a GET avail search request with {string}, {string}, {string}, {string}, {string}', function (checkIn,checkOut,packageGroup,rooms,zoneId) {
  spec.get("https://apihom.services.cvc.com.br/sub-backend-hotels-store/hotels?checkIn=" + checkIn + "&checkOut=" + checkOut + "&packageGroup=" + packageGroup + "&rooms=" + rooms + "&zoneId=" + zoneId");
  spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.eSI7adU620BGiO2X8C3uVxkEvHdzhIJmyIuyegvB8ME');
  spec.withHeaders('gtw-transaction-id', 'b74a9265-fdd0-297a-8527-c3d57fb16b95-1631194638376');
  spec.withHeaders('gtw-search-token', 'ee0788c79f683ef9f5a02b5c3ed35579');
});

When('I receive a response avail search', async function () {
  await spec.toss();
});

Then('response avail search should have a status {int}', async function (code) {
  spec.response().should.have.status(code);
});
