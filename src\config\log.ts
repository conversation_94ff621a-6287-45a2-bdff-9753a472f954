import { injectable, inject } from 'inversify';
import moment from 'moment';

@injectable()
class Log
{
	private owner = "";

	constructor()
	{
	}

	debug( message, header? )
	{
		this.#log("debug", message, header );
		return this;
	}
	info( message, header? )
	{
		this.#log("info", message, header );
		return this;
	}
	warn( message, header? )
	{
		this.#log("warn", message, header );
		return this;
	}
	error( message, header? )
	{
		this.#log("error", message, header );
		return this;
	}

	#log( type, message, header? )
	{
		const builtContext = header && this.#buildContext( header ) || "";
		console[ type ](`\n${type.toUpperCase()} ${builtContext} [ ${moment().format('YYYY-MM-DD HH:mm:ss')} ]${this.owner}:`);
		console.log( message );
	}

	setOwner( who )
	{
		this.owner = `[ ${who} ]`
		return this;
	}

	#buildContext( context: any )
	{
		if ( Object.prototype.toString.call( context ) === "[object String]" )
		{
			return context;
		}

		let built = "";
		for ( var i in context )
		{
			const pack = built.concat( `[ ${i}: ${context[ i ]} ]` )
			built = pack;
		}

		return built;
	}

}

export default Log;
