import Log from "../config/log";

const log = new Log();

type Pack = object | number | string | boolean | undefined | any[];

import potential from "./potential";

class FilteringAndOrdering
{

	private isAsc = true;
	private collection = null;
	private static splitWithComma = /,/;
	private static splitWithDot = /\./;
	private static splitWithColon = /:/;
	private static bracketsEmbraced = /\[.+?\]/;
	private UNION = "union";
	public static nonASCII = /[^\w]/g;
	public static splitWithDoubleDot = /\.{2}/;


	constructor( query, collection )
	{
		this.collection = collection;
		this.fillOrderAndFilterFromQuery( query );
	}

	public getFilteredAndOrdered()
	{
		return this.collection;
	}

	private fillOrderAndFilterFromQuery( query )
	{
		if ( /desc/.test(query.sortOrder) === true )
		{
			this.isAsc = false;
		}
		const sortBy = query.sortBy?.split( FilteringAndOrdering.splitWithComma ).map( each => String(each).trim() );
		const sortBySelected = [];
		sortBy?.forEach
		(
			( each ) =>
			{
				potential.sorters.forEach
				(
					( sorter ) =>
					{
						if ( sorter.key === each )
						{
							if ( /preferential/.test(each) === true )
							{
								sortBySelected.push( potential.foundAt["rank"] );
								sortBySelected.push( "invertedPreferential" );
								this.collection.forEach
								(
									each =>
									{
										each.invertedPreferential = ! each.isPreferential;
										if ( each.rank === undefined )
										{
											//Just to push to the end when sorting
											each.rank = "undefined";
										}
									}
								)
							}
							else
							{
								sortBySelected.push( potential.foundAt[ sorter.key ] );
							}
						}
					}
				)
			}
		);

		let price = false;

		sortBySelected.forEach
		(
			each =>
			{
				if ( /price/.test(each) === true )
				{
					price = true
				}
			}
		);

		if ( price === false )
		{
			sortBySelected.unshift(potential.foundAt["price"]);
		}

		const filterBy = [];
		potential.filters.forEach
		(
			( each ) =>
			{
				const queryByEachKey = query[ each.key ];
				if 
				( 
					this.isEligibleToFilter( queryByEachKey ) === true
				)
				{
					filterBy.push
					( 
						{
							by: each.key,
							values: queryByEachKey.split( FilteringAndOrdering.splitWithComma ).map( each => String(each).trim() ),
							foundAt: potential.foundAt[ each.key ],
							type: each.type,
							using: each.function
						}
					);
				}
			}
		);

		if ( filterBy.length > 0 )
		{
			this.applyFilter( filterBy );
		}
		this.applySorter( sortBySelected );
		
	}

	private applyFilter( filtering )
	{
		let indexes;
		let unionCollection = [];
		filtering.forEach
		(
			filter =>
			{
				filter.values.forEach
				(
					value =>
					{
						//
						indexes = FilteringAndOrdering.filterBy
						(
							this.collection, value, filter.foundAt, this[filter.using] 
						);
						if ( !! filter.type === false )
						{
							this.collection = this.collectionByIndexes( indexes );
						}
						else if ( filter.type === this.UNION )
						{
							unionCollection = 
							[ 
								... unionCollection, 
								... this.collectionByIndexes( indexes )  
							];
						}
					}
				);
				if ( filter.type === this.UNION )  
				{
					this.collection = unionCollection;
					unionCollection = [];
				}
			}
		);
	}

	private collectionByIndexes( indexes ): any[]
	{
		let uniq = indexes.filter( (item, index, array) => array.indexOf( item ) === index );
		const selected = [];
		uniq.forEach
		(
			each =>
			{
				selected.push( this.collection[ each ] );
			}
		);
		return selected;
	}

	private applySorter( sorting )
	{
		this.collection = FilteringAndOrdering.orderBy( this.collection, sorting, this.isAsc );
	};

	static recurseToFind( packToValue: Pack, way: string[], priorWay?, shouldLog: number = 0 )
	{
		if ( packToValue === undefined )
		{
			if ( shouldLog === 0 )
			{
				log.warn(`Could not reach ${priorWay} when searching through pack/collection`)
			}
			return null;
		}

		if ( way.length === 0 )
		{
			return packToValue;
		}

		let cropWay = way.splice(0, 1)[0];

		return this.recurseToFind( packToValue[ cropWay ], way, cropWay, shouldLog );
	};

	static recurseToFindMany( packToValues, way: string[] )
	{
		if ( way.length <= 0 )
		{
			return packToValues;
		}
		let cropWay = way.splice(0, 1)[0];
		if ( this.bracketsEmbraced.test(cropWay) === true )
		{
			let lookedFor = [];
			const buildLookedFor = cropWay.replace(/\[|\]/g, "");
			const buildLookedForSplit = buildLookedFor.split(/\|/);

			if ( Array.isArray(packToValues))
			{
				for ( const each of packToValues )
				{
					if ( Array.isArray( each[buildLookedForSplit[0]] ) )
					{
						lookedFor = [ ...lookedFor, ...each[buildLookedForSplit[0]] ]
					}
				}
				return this.recurseToFindMany( lookedFor, way );
			}
			else
			{
				lookedFor = this.recurseToFind( packToValues, buildLookedForSplit[0].split(FilteringAndOrdering.splitWithDot) );
				if ( lookedFor === null )
				{
					return [];
				}
				const selected = [];
				for ( const each of lookedFor )
				{
					if ( buildLookedForSplit.length > 1 )
					{
						const splitKeyValue = buildLookedForSplit[ 1 ].split( this.splitWithColon )
						if ( each[ splitKeyValue[0] ] === splitKeyValue[1] )
						{
							selected.push( each );
						}
					}
					else
					{
						selected.push( each );
					}
				}
				return this.recurseToFindMany( selected, way );
			}

		}
		else
		{
			const selected = [];
			for ( const each of packToValues )
			{
				selected.push( each[ cropWay ]);
			}
			return this.recurseToFindMany( selected, way );
		}
	};

	static filterBy( collection: any[], by: string, where: string, using: (filter, value) => boolean ): number[]
	{
		//
		const indexes = [];
		collection.forEach
		(
			(each, index) =>
			{
				let lookedFor;
				if ( FilteringAndOrdering.splitWithDoubleDot.test( where ) === true )
				{
					try
					{
						const byPrepared = by.replace( this.nonASCII, "" )
						lookedFor = this.recurseToFindMany( each, where.split( FilteringAndOrdering.splitWithDoubleDot ) );
						for ( const each of lookedFor )
						{
							const eachPrepeared = String(each).replace( this.nonASCII, "" );
							if ( using( byPrepared, eachPrepeared ) === true )
							{
								indexes.push( index );
							}
						}
					}
					catch( error )
					{
						indexes.push( index );
					}
				}
				else
				{
					lookedFor = this.recurseToFind( each, where.split(FilteringAndOrdering.splitWithDot), undefined, index );
					if ( lookedFor === null )
					{
						return true;
					}
					if ( using( by, String(lookedFor) ) === true )
					{
						indexes.push( index );
					}
				}
			}
		);
		return indexes;
	};

	static orderBy( what: any[], by: string[], orderAsc: boolean = true ): any[]
	{

		let refWhat = what;

		for ( var i = 0 ; i < by.length ; i++ )
		{
			const contentBy = [];
			let ordered = [];

			for ( var v = 0 ; v < refWhat.length ; v++ )
			{
				const iter = 
				{ 
					value: this.recurseToFind( refWhat[ v ], by[ i ].split( /\./ ), undefined, v ), 
					index: v 
				};
				if ( iter.value === null )
				{
					iter.value = "";
				}
				contentBy.push( iter );
			}

			contentBy.sort
			(
				( a, b ) =>
				{
					if ( orderAsc === true )
					{
						return a.value >= b.value && 1 || -1
					}
					return b.value >= a.value && 1 || -1
				}
			);

			contentBy.forEach
			(
				( each, key ) =>
				{
					ordered.push( refWhat[ each.index ] );
				}
			);

			refWhat = ordered;
		}

		return refWhat;
		
	}
	
	private match( filter, value ): boolean
	{
		const regex = new RegExp(filter, "i");
		const doesMatch = regex.test( value );
		return doesMatch;
	}

	private equal( filter, value ): boolean
	{
		return filter === value;
	}

	private different( filter, value ): boolean
	{
		return filter !== value;
	}

	private lessThan( filter, value ): boolean
	{
		const numberedFilterAndValue = [ Number(filter), Number(value) ];
		if 
		(
			isNaN(numberedFilterAndValue[ 0 ]) === false &&
			isNaN(numberedFilterAndValue[ 1 ]) === false
		)
		{
			const isLessThan = numberedFilterAndValue[ 1 ] < numberedFilterAndValue[ 0 ];
			return isLessThan;
		}
		return value < filter
	}

	private lessEquals( filter, value ): boolean
	{
		const numberedFilterAndValue = [ Number(filter), Number(value) ];
		if 
		(
			isNaN(numberedFilterAndValue[ 0 ]) === false &&
			isNaN(numberedFilterAndValue[ 1 ]) === false
		)
		{
			const isLessEquals = numberedFilterAndValue[ 1 ] <= numberedFilterAndValue[ 0 ];
			return isLessEquals;
		}
		return value <= filter
	}


	private greaterThan( filter, value ): boolean
	{
		const numberedFilterAndValue = [ Number(filter), Number(value) ];
		if 
		(
			isNaN(numberedFilterAndValue[ 0 ]) === false &&
			isNaN(numberedFilterAndValue[ 1 ]) === false
		)
		{
			return numberedFilterAndValue[ 1 ] > numberedFilterAndValue[ 0 ];
		}
		return value > filter
	}

	private isEligibleToFilter( filterBy ): boolean
	{
		return !! filterBy === true && filterBy !== "null" && filterBy !== "undefined";
	}

}

export default FilteringAndOrdering;



