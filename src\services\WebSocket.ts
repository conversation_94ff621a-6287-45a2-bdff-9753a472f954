import { injectable, inject } from 'inversify';
import { TYPES } from '../config/types';
import { getEnv } from "../utils";
import io from "socket.io-client";
import Log from "../config/log";

class WebSocketIO
{

	private socket;
	private key;
	private data;
	private haltLog = false;
	private log = new Log();

	constructor()
	{
		this.connect();
	}

	connect( )
	{
		const url = getEnv("WEBSOCKET_SERVER_URL");

		this.socket = io
		( 
			url, 
			{ 
				transports: [ "websocket" ], 
				path: "/socket.io"
			} 
		);

		this.socket.on
		(
			"connect",
			( ) =>
			{
				this.log.debug(`Websocket connected:${url}, sid: ${this.socket.id}`);
				this.haltLog = false;
			}
		);
		this.socket.on
		(
			"disconnect",
			( reason ) =>
			{
				this.log.debug("Websocket disconnected");
				if ( reason === "io server disconnect")
				{
					this.connect();
				}
			}
		);

		this.socket.on
		(
			"connect_error",
			( error ) =>
			{
				if ( this.haltLog === false )
				{
					this.log.error( error.toString() );	
					this.haltLog = true;
				}
			}
		)

	}

	setLog( log )
	{
		this.log = log;
	}

	isConnected()
	{
		return this.socket.connected;
	}

	emit( key, data )
	{
		this.socket.emit( key, data );
	}

}

export default new WebSocketIO();

