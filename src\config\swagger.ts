

import { injectable } from 'inversify';
import * as swaggerUi from "swagger-ui-restify";
const swaggerDocument = require("../../swagger.document.json");

@injectable()
export default class Swagger
{
	private path = "/docs/swagger";

	private swaggerOptions = 
	{
		explorer: true,
		baseURL: this.path,
		path: this.path
	}


	config( app )
	{
		app.get( this.path + "/*", ...swaggerUi.serve );
		app.get( this.path, swaggerUi.setup( swaggerDocument, this.swaggerOptions ) );
	}
}
