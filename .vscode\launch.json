{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "QA",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/src/index.ts",
      "sourceMaps": true,
      "preLaunchTask": "npm: build",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "env": {
        "LOG_HOST": "logstash-qa.services.cvc.com.br",
        "LOG_PORT": "12201",
        "PREFIX": "sub-backend-hotels-store",
        "NODE_ENV": "dev",
        "CONSUL_KEY_PREFIX_ID_AIM_OSB": "prefix.ids.aim.osb",
        "CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH": "stores.importLoc",
        "CONSUL_KEY_MASTER_SWITCH": "master.switch",
        "HOTEL_BACK_FOR_FRONT_GATEWAY_URL": "http://hotel-back-for-front.k8s-qa-cvc.com.br",
        "HOTEL_BACK_FOR_FRONT_OSB_URL": "http://osb-qa.services.cvc.com.br",
        "BROKERS": "Extranet,Juniper,Omnibees,JuniperCorp,Hotelbeds,CVC",
        "CONSUL_PORT": "8500",
        "CONSUL_URL": "consul-qa.services.cvc.com.br",
        "CONSUL_HOST": "consul-qa.services.cvc.com.br",
        "CONSUL_KEY_VALUE_TREE": "sub-backend-hotels-store",
        "CONSUL_KEY_FRANCHISE_SWITCH": "stores.switch",
        "HEADER_NAME_LABEL_REDIS_KEY": "gtw-search-token",
        "HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY": "gtw-sec-websocket-key",
        "HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN": "gtw-sec-user-token",
        "WEBSOCKET_SERVER_URL": "wss://websocket-hom.services.cvc.com.br",
        "AWS_REGION": "us-east-1"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "PROD",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/src/index.ts",
      "sourceMaps": true,
      "preLaunchTask": "npm: build",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "env": {
        "LOG_HOST": "logstash.services.cvc.com.br",
        "LOG_PORT": "12201",
        "PREFIX": "sub-backend-hotels-store",
        "NODE_ENV": "prod",
        "CONSUL_KEY_PREFIX_ID_AIM_OSB": "prefix.ids.aim.osb",
        "CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH": "stores.importLoc",
        "CONSUL_KEY_MASTER_SWITCH": "master.switch",
        "HOTEL_BACK_FOR_FRONT_GATEWAY_URL": "http://hotel-back-for-front.k8s-cvc.com.br",
        "HOTEL_BACK_FOR_FRONT_OSB_URL": "https://api.aws.cvc.com.br/bookinghotel",
        "BROKERS": "Extranet,Juniper,Omnibees,JuniperCorp,Hotelbeds,CVC",
        "CONSUL_PORT": "8500",
        "CONSUL_URL": "consul-prod.services.cvc.com.br",
        "CONSUL_HOST": "consul-prod.services.cvc.com.br",
        "CONSUL_KEY_VALUE_TREE": "sub-backend-hotels-store",
        "CONSUL_KEY_FRANCHISE_SWITCH": "stores.switch",
        "REDIS_HOST": "cacheprdhotels-sp.xqdekl.ng.0001.sae1.cache.amazonaws.com",
        "REDIS_PORT": "6379",
        "HEADER_NAME_LABEL_REDIS_KEY": "gtw-search-token",
        "HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY": "gtw-sec-websocket-key",
        "HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN": "gtw-sec-user-token",
        "WEBSOCKET_SERVER_URL": "https://b2b-websocket.services.cvc.com.br",
        "AWS_REGION": "us-east-1"
      }
    }
  ]
}
