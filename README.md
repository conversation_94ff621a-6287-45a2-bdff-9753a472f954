# sub-backend-hotels-store

BFF para o site do Atlas. (🏪LOJA)

---

## 🚀 Como rodar o projeto localmente

### Pré-requisitos

- **Node.js** (versão recomendada: v14.15.5)
- **NPM** (gerenciador de pacotes do Node.js)

### Instruções

1. Clone o repositório:

   ```bash
   <NAME_EMAIL>:Desenvolvimento-MS/sub-backend-hotels-store.git
   cd sub-backend-hotels-store
   ```

2. Instale as dependências:

   ```bash
   npm install
   ```

3. Execute o projeto para o ambiente QA:

   ```bash
   npm run dev:qa
   ```

   ou utilize o _launcher QA_ configurado no **VSCode**.

4. O projeto estará disponível localmente em:

   ```
   http://localhost:8091
   ```

---

## 🌐 URLs dos Ambientes

### Ambiente de TI

- **URL Base:** [https://sub-backend-hotels-store.k8s-ti-cvc.com.br](https://sub-backend-hotels-store.k8s-ti-cvc.com.br)
- **Swagger:** [https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api-doc](https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api-doc)

### Ambiente de QA

- **URL Base:** [https://sub-backend-hotels-store.k8s-qa-cvc.com.br](https://sub-backend-hotels-store.k8s-qa-cvc.com.br)
- **Swagger:** [https://sub-backend-hotels-store.k8s-qa-cvc.com.br/api-doc](https://sub-backend-hotels-store.k8s-qa-cvc.com.br/api-doc)

### Ambiente de Produção (PROD)

- **URL Base:** [https://sub-backend-hotels-store.k8s-cvc.com.br](https://sub-backend-hotels-store.k8s-cvc.com.br)
- **Swagger:** [https://sub-backend-hotels-store.k8s-cvc.com.br/api-doc](https://sub-backend-hotels-store.k8s-cvc.com.br/api-doc)

---

## 🔌 Endereço do WebSocket

- **Ambiente QA:** [https://websocket-hom.services.cvc.com.br/](https://websocket-hom.services.cvc.com.br/)
- **Ambiente PROD:** [https://b2b-websocket.services.cvc.com.br/](https://b2b-websocket.services.cvc.com.br/)

Para pegar o response do WebSocket, é necessário na chamada do endpoint do BFF passar o header `gtw-sec-websocket-key` com o valor do token apresentado no site acima.
Os responses vão aparecer no site acima.

---

## 🗂️ Configurações no Consul

As configurações do projeto estão armazenadas no Consul para os diferentes ambientes:

- **Ambiente TI:** [http://consul-dev.services.cvc.com.br:8500/ui/dc1/kv/sub-backend-hotels-store/](http://consul-dev.services.cvc.com.br:8500/ui/dc1/kv/sub-backend-hotels-store)
- **Ambiente QA:** [http://consul-qa.services.cvc.com.br:8500/ui/dc1/kv/sub-backend-hotels-store/](http://consul-qa.services.cvc.com.br:8500/ui/dc1/kv/sub-backend-hotels-store)
- **Ambiente PROD:** [http://consul-prod.services.cvc.com.br:8500/ui/dc1/kv/sub-backend-hotels-store/](http://consul-prod.services.cvc.com.br:8500/ui/dc1/kv/sub-backend-hotels-store)

---

## 📄 Endpoints Principais

Certifique-se de acessar o Swagger correspondente ao ambiente para obter a lista completa de endpoints disponíveis e sua documentação detalhada.

- **Swagger Local:** [http://localhost:8091/api-doc](http://localhost:8091/api-doc)
- **Swagger TI:** [https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api-doc](https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api-doc)
- **Swagger QA:** [https://sub-backend-hotels-store.k8s-qa-cvc.com.br/api-doc](https://sub-backend-hotels-store.k8s-qa-cvc.com.br/api-doc)
- **Swagger PROD:** [https://sub-backend-hotels-store.k8s-cvc.com.br/api-doc](https://sub-backend-hotels-store.k8s-cvc.com.br/api-doc)

---

## Exemplo do Avail local

```bash
curl --location 'http://localhost:8091/hotels?checkIn=2025-02-11&checkOut=2025-02-18&packageGroup=STANDALONE&rooms=30%2C30&zoneId=747&validate=&sortBy=preferential&sortOrder=ascending&transactionId=transactionId-CVC-29eb9e0b-b6d1-42c3-8ec6-c07b4a19a06e' \
--header 'accept: application/json, text/plain, */*' \
--header 'accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7' \
--header 'gtw-pricing: PF' \
--header 'gtw-sec-user-token: eyJ4NXQjUzI1NiI6IkdNcmtZeEgxdFdqelBqTFNlNl9xZmdVdkxncVRtU3FaeGdTTGZmcDF3Q0EiLCJraWQiOiJjb3JwYXV0aG9yaXphdGlvbnNlcnZlci52MiIsImFsZyI6IlJTMjU2In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pzapAIydsaMESa5iYlwhenAY7sXy0HZMc_lZ9MPMndF4DhV_O9NjlT6ilM0_frO9YI4LD5hzPTeMVQElFHTBoGk1fashiufEbG1r_JikJX_oyxO_35wEDJ96AYc2tOiY9xraPawVe3nVtQdVP0qrKB8YQHvOQXuKPnRphAgpuDUiHlkh3YZk5Eqzcwqp4wVjTI17aHjZ61qJO8vS0LOl-28nMuPE0J5w3xT9TPKd1RQtmlSAj1e3OeqEQAho4Suy_wRGFbBL36MdcyBR_u5AaQlEjFv9fwhA3lDM_ECYRXKJYlPnnr6cpXIuIgUgI8PFV_lN3oNOMwAG0kBZpasq-Q' \
--header 'gtw-sec-websocket-key: ********************************************************' \
--header 'gtw-transaction-id: transactionId-CVC-29eb9e0b-b6d1-42c3-8ec6-c07b4a19a06e' \
--header 'priority: u=1, i' \
--header 'referer: https://atlas.cvc.com.br/' \
--header 'sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"' \
--header 'sec-ch-ua-mobile: ?0' \
--header 'sec-ch-ua-platform: "Windows"' \
--header 'sec-fetch-dest: empty' \
--header 'sec-fetch-mode: cors' \
--header 'sec-fetch-site: same-origin' \
--header 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36' \
--header 'x-dtpc: 13$463233999_364h16vBTLQOEMCPICJKKRHAOFSWSFAFCGJUKCH-0e0'
```
