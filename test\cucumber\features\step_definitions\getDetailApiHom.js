const pactum = require('pactum');
const { Given, When, Then, Before } = require('@cucumber/cucumber');

let spec = pactum.spec();

Before(() => { spec = pactum.spec(); });

Given('I make a GET request api hom detail with {string}', function (idHotel) {
  spec.get("https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api/hotels/" + idHotel);
  spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.eSI7adU620BGiO2X8C3uVxkEvHdzhIJmyIuyegvB8ME');
  spec.withHeaders('gtw-transaction-id', 'b74a9265-fdd0-297a-8527-c3d57fb16b95-1631194638376');
});

When('I receive a response api hom detail', async function () {
  await spec.toss();
});

Then('response api hom detail should have a status {int}', async function (code) {
  spec.response().should.have.status(code);
});
