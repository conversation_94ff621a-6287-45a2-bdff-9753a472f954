import axios from "axios";

export default class Opportunities {


	constructor() {

	}

	async getPromotions(packageRooms, secUserToken, transactionId) {

		try {

			const selectedItems = packageRooms.map((pr, index) => {

				return {
					rph: index + 1,
					rateToken: pr.rates[0].rateToken
				}

			});

			const url = process.env.URL_OPPORTUNITIES;

			const resp = await axios.post(url, {
				"params": {
					"showSteps": false, "showPayloads": false, "profileName": "hot-detail"
				},
				"selectedItems": selectedItems,
				"availableItems": []
			}, {
				headers: {
					'Content-Type': 'application/json',
					'gtw-sec-user-token': secUserToken,
					'gtw-transaction-id': (transactionId) ? transactionId : "xxxxx"
				}
			});

			if (resp && resp.data && resp.data.selectedItems) {

				resp.data.selectedItems.forEach((selectedItem, index) => {

					if (selectedItem) {

						Object.assign(packageRooms[index].rates[0], {
							...selectedItem
						})

					}

				});

			}

		}
		catch (err) {

			return packageRooms;

		}

	};
}
