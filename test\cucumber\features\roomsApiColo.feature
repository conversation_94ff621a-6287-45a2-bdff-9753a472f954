Feature: Test rooms

  Scenario Outline: Test rooms
    Given I make a GET request api colo rooms with <idHotel>, <checkIn>, <checkOut>, <source>, <packageGroup>, <rooms>, <zoneId>
    When I receive a response api colo rooms
    Then response api colo rooms should have a status 200
    Examples:
      | idHotel  | checkIn    | checkOut   | source    | packageGroup | rooms   | zoneId |
      | 381110   | 2022-07-12 | 2022-07-15 | EXTRANET  | STANDALONE   | 30%2C30 | 36132  |
