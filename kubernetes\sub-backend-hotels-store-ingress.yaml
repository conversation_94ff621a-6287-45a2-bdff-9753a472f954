apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: sub-backend-hotels-store-ingress
  namespace: corp-hotels
  annotations:
    kubernetes.io/ingress.class: "nginx-private"
    nginx.org/ssl-backends: "sub-backend-hotels-store-service"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  rules:
    - host: sub-backend-hotels-store.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - backend:
              serviceName: sub-backend-hotels-store-service
              servicePort: 80
