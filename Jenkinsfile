@Library('cvc-jen<PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "sub-backend-hotels-store",
    "git": {
        "repositoryUrl": "******************:Desenvolvimento-MS/sub-backend-hotels-store.git"
    },
    "technology": {
        "name": "nodejs",
        "version": "14.15.1",
        "dependencyManager": "NPM",
        "buildCommands": {
            "buildApp": "npm install && npm run build"
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "corp-hotels"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
}