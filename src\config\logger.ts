import bunyan from "bunyan";
import moment from "moment";
import os from "os";
import gelf from "gelf";

function ConsoleStream() { }

ConsoleStream.prototype.write = (data) => {
  data = JSON.parse(data);

  let type = "";

  switch (data.level) {
    case 20:
      type = "debug";
      break;
    case 30:
      type = "info";
      break;
    default:
      type = "error";
      break;
  }

  console[type](`${type.toUpperCase()} ${data.name} [ ${moment().format("YYYY-MM-DD HH:mm:ss")} ]: ${data.msg}`);
};

function LogstashStream(options) {
  this.client = null;
  this.options = options;
}

LogstashStream.prototype.write = function write(entry) {
  if (typeof (entry) === "string") {
    entry = JSON.parse(entry);
  }

  const ignore = ["hostname", "time", "msg", "name", "level", "v", "pid", "tags", "payload"];

  const levels = {
    10: { name: "trace", level: 0 },
    20: { name: "debug", level: 7 },
    30: { name: "info", level: 6 },
    40: { name: "warn", level: 4 },
    50: { name: "error", level: 3 },
    60: { name: "fatal", level: 2 }
  };

  const tags = ["LOJAS", "STORE", "SUB-BACKEND-HOTELS-STORE"];

  if (entry.tags) {
    tags.push(...entry.tags);
  }

  if (entry.operation) {
    tags.push(entry.operation);
  }

  const pattern = {
    type: "gelf",
    version: "1.1",
    host: os.hostname(),
    timestamp: entry.time,
    level: levels[entry.level].level,
    _level_name: levels[entry.level].name,
    pid: entry.pid,
    _message: (entry.msg || "").substring(0, 20000),
    _app_name: entry.name,
    _tags: tags,
    productType: "lojas",
    channel: "WEB"
  };

  if (entry.payload) {
    pattern["payload"] = (entry.payload || "").substring(0, 20000);
  }

  for (const key in entry) {
    if (!Object.prototype.hasOwnProperty.call(entry, key)) continue;

    if (ignore.indexOf(key) < 0 && pattern[key] == null) {
      pattern[`_${key}`] = entry[key];
    }
  }

  this.send(pattern);
};

LogstashStream.prototype.send = function send(message) {
  const self = this;

  if (!self.client) {
    self.client = new gelf({
      graylogPort: self.options.port,
      graylogHostname: self.options.host
    });
  }
  self.client.emit("gelf.log", message);
};

const logger = bunyan.createLogger({
  name: process.env.PREFIX,
  serializers: {
    req: bunyan.stdSerializers.req,
    es: bunyan.stdSerializers.res
  },
  streams: [
    {
      level: "debug",
      stream: new ConsoleStream()
    },
    {
      type: "raw",
      stream: new LogstashStream({
        port: process.env.LOG_PORT,
        host: process.env.LOG_PORT
      })
    }
  ]
});

export default logger;
