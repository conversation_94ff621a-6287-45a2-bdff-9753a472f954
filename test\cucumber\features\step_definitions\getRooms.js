const pactum = require('pactum');
const { Given, When, Then, Before } = require('@cucumber/cucumber');

let spec = pactum.spec();

Before(() => { spec = pactum.spec(); });

Given('I make a GET request rooms with {string}, {string}, {string}, {string}, {string}, {string}, {string}', function (idHotel, checkIn, checkOut, hotelId, packageGroup, rooms, zoneId) {
  spec.get("https://sub-backend-hotels-store.k8s-ti-cvc.com.br/api/hotels/" + idHotel + "/rooms?checkIn=" + checkIn + "&checkOut=" + checkOut + "&hotelId=" + hotelId + "&packageGroup=" + packageGroup + "&rooms=" + rooms + "&zoneId=" + zoneId);
  spec.withHeaders('gtw-sec-user-token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.eSI7adU620BGiO2X8C3uVxkEvHdzhIJmyIuyegvB8ME');
  spec.withHeaders('gtw-sec-websocket-key', 'aHR0cDovLzE3Mi4zMC4xNTEuMjQ2OjgwfFFzTnUtQ2IxTmotcktPc0hBQWRK');
  spec.withHeaders('gtw-transaction-id', 'b74a9265-fdd0-297a-8527-c3d57fb16b95-1631194638376');
});

When('I receive a response rooms', async function () {
  await spec.toss();
});


Then('response rooms should have a status {int}', async function (code) {
  spec.response().should.have.status(code);
});