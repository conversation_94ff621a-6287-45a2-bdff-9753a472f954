const potential = 
{
	sorters:
	[
		{
			key: "price"
		},
		{
			key: "name"
		},
		{
			key: "preferential",
			specialCare: "invert"
		},
		{
			key: "rank"
		}
	],
	filters:
	[
		{
			key: "name",
			function: "match"
		},
		{
			key: "awards",
			function: "equal",
			type: "union"
		},
		{
			key: "amenities",
			function: "match",
			type: "union"
		},
		{
			key: "maxPrice",
			function: "lessEquals"

		},
		{
			key: "minPrice",
			function: "greaterThan"
		},
		{
			key: "mealPlan",
			function: "match",
			type: "union"
		},
	    {
			key: "searchFeb",
			function: "different"
		}
	],
	foundAt:
	{
		price:"rooms.0.rates.0.priceWithTax",
		priceWithoutTax: "rooms.0.rates.0.priceWithoutTax",
		name: "name",
		awards: "award",
		maxPrice: "rooms.0.rates.0.priceWithTax",
		minPrice: "rooms.0.rates.0.priceWithTax",
		maxPriceWithoutTax: "rooms.0.rates.0.priceWithoutTax",
		minPriceWithoutTax: "rooms.0.rates.0.priceWithoutTax",
		mealPlan: "rooms.0.mealPlan",
		rank: "rank",
		amenities: "[contents|code:amenities]..[items]..name",
		preferential: "isPreferential",
	    searchFeb: "rooms.0.isAvailable",
		regionName: "resort.location.region.name",
	}
}

export default potential;
const foundAt = potential.foundAt;
export { foundAt };


