import { inject, injectable } from "inversify";
import { Controller, Get, Post, interfaces } from "inversify-restify-utils";
import { Request, Response } from "restify";
import { TYPES } from "../config/types";
const stringifyCircular = require("json-stringify-safe");

import { buildRedisCache, getEnv, injectValidateOne, thereIsCache } from "../utils";

import FilteringAndOrdering from "../FilteringAndOrdering";
import MetaBuilder from "../MetaBuilder";

import Paging from "../Paging";
import Log from "../config/log";
import BackForFront from "../services/BackForFront";
import consul from "../services/Consul";
import redis from "../services/Redis";

import logger from "../config/logger";
import { StopWatch } from "../config/stopwatch";
import { DetailResponse } from "../models/DetailResponse";
import Opportunities from "../services/Opportunities";

@Controller("/")
@injectable()
export class Main implements interfaces.Controller {
  private log;

  constructor(@inject(TYPES.Log) log) {
    this.log = log;
  }

  private getParams(bff: BackForFront) {
    const url = `${bff["baseURL"]}/${bff["basePath"]}/${bff["path"]}`;
    const agentSign = bff["headers"]["Gtw-Agent-Sign"];
    const branchId = bff["headers"]["Gtw-Branch-Id"];
    return { url, agentSign, branchId };
  }

  // ROOMS OR HASAVAIL
  @Get("/hotels/:id/rooms")
  public async roomsOrHasAvail(req: Request, res: Response) {
    console.log("############### ROOMS OR HASAVAIL ###############");

    const transactionId: string = req.headers["gtw-transaction-id"].toString();
    const hotelId = req.params.id;

    const timer = new StopWatch();
    timer.start();

    let path = `${req.params.id}/rooms`;
    let requestType = "rooms";
    let ignorePriceChange: string | undefined;
    let packageService: string | undefined;

    if (req.headers.rateToken !== undefined || req.headers.ratetoken !== undefined) {
      var rateToken = req.headers.rateToken || req.headers.ratetoken;

      path = `${req.params.id}/rooms/${rateToken}`;
      requestType = "hasAvail";
      ignorePriceChange = req.params.ignorePriceChange as string;
      packageService = req.params.packageService as string;
    }

    try {
      const bff = new BackForFront(consul, req, path);

      let response;

      if (requestType === "rooms") response = await bff.getRooms(req.query);

      if (requestType === "hasAvail") response = await bff.getHasAvail(hotelId, ignorePriceChange, packageService);

      if (response.message) {
        response.transactionId = transactionId;
        res.json(response);
        return;
      }

      const installment = process.env.INSTALLMENT ? parseInt(process.env.INSTALLMENT) : null;

      if (requestType === "rooms") {
        let eligible = response.data.rooms?.length;
        if (
          response &&
          response.data &&
          response.data.rooms &&
          response.data.rooms[0] &&
          response.data.rooms[0].error
        ) {
          eligible = 0;
        }

        var hasDeduplicate = req.headers["gtw-deduplicate"] === "true";

        if (eligible > 0) {
          const orderedRooms = {
            installment,
            transactionId,
            rooms: hasDeduplicate
              ? response.data.rooms
              : FilteringAndOrdering.orderBy(response.data.rooms, ["rates.0.priceWithTax"]),
          };
          res.json(orderedRooms);
        } else {
          res.json({ transactionId, rooms: [] });
        }
      } else {
        response.data.transactionId = transactionId;
        response.data.installment = installment;

        if (response.data && response.data.packageRooms) {
          try {
            const { packageRooms } = response.data;
            const secUserToken = req.headers["gtw-sec-user-token"];

            const opportunities = new Opportunities();
            await opportunities.getPromotions(packageRooms, secUserToken, transactionId);
          } catch (err) {}
        }

        res.json(response.data);
      }
    } catch (error) {
      const { message } = error;
      res.status(500);
      res.json({ message, transactionId });
    }
  }

  async delay(ms: number) {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  @Post("/timeout")
  public async timeout(req: Request, resp: Response) {
    const ms: number = req.body["ms"];
    await this.delay(ms);
    return "OK";
  }

  // DETAILS
  @Get("/hotels/:id")
  public async details(req: Request, res: Response) {
    console.log("############### DETAILS ###############");

    const transactionId: string = req.headers["gtw-transaction-id"].toString();

    try {
      const hotelId = req.params.id;
      const path = `${req.params.id}`;
      const bff = new BackForFront(consul, req, path);

      const response = await bff.getDetail(hotelId);
      const hotelResponse: DetailResponse = response.data;
      hotelResponse.transactionId = transactionId;

      res.json(hotelResponse);
    } catch (error) {
      const { message } = error;
      res.status(500);
      res.json({ message, transactionId });
    }
  }

  @Get("/resorts")
  public async asyncResorts(req: Request, res: Response) {
    const transactionId: string = req.headers["gtw-transaction-id"].toString();

    try {
      const bff = new BackForFront(consul, req);

      req.query["resorts"] = true;
      if ((await thereIsCache(req, res, bff, redis)) === true) return;

      bff.reachWebSocketHotelConsul(req.query, redis);

      res.json({ status: "PROCESSING SEARCH", mechanisms: { async: true, redis: false }, transactionId });
    } catch (error) {
      const { message } = error;
      res.status(500);
      res.json({ message, transactionId });
    }
  }

  @Get("/disney")
  public async asyncDisney(req: Request, res: Response) {
    console.log("############### ASYNC DISNEY ###############");

    const transactionId: string = req.headers["gtw-transaction-id"].toString();

    try {
      const bff = new BackForFront(consul, req);

      req.query["disney"] = true;
      if ((await thereIsCache(req, res, bff, redis)) === true) return;

      bff.reachWebSocketHotelConsul(req.query, redis);

      res.json({ status: "PROCESSING SEARCH", mechanisms: { async: true, redis: false }, transactionId });
    } catch (error) {
      const { message } = error;
      res.status(500);
      res.json({ message, transactionId });
    }
  }

  @Get("/hotels")
  public async asyncAvail(req: Request, res: Response) {
    console.log("############### ASYNC AVAIL ###############");

    const transactionId: string = req.headers["gtw-transaction-id"].toString();

    try {
      const bff = new BackForFront(consul, req);
      const noWebsocket = bff.noWebsocket;

      const { checkIn, checkOut } = req.query;

      bff.validDates(checkIn, checkOut);

      if ((await thereIsCache(req, res, bff, redis)) === true) {
        return;
      }

      if (!!req.headers[getEnv("HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY")] === false && !noWebsocket) {
        //Now it became SYNC!
        this.coreSyncAvail(req, res, bff);
        return;
      }

      bff.reachToWebsocketServer(req.query, "get", redis);

      res.json({ status: "PROCESSING SEARCH", mechanisms: { async: true, redis: false }, transactionId });
    } catch (error) {
      const { message } = error;
      res.status(500);
      res.json({ message, transactionId });
    }
  }

  @Get("/sync/hotels")
  public async syncAvail(req: Request, res: Response) {
    console.log("############### SYNC AVAIL ###############");

    try {
      const bff = new BackForFront(consul, req);

      if ((await thereIsCache(req, res, bff, redis)) === true) {
        return;
      }

      this.coreSyncAvail(req, res, bff);
    } catch (error) {
      this.sendError(res, error);
    }
  }

  private async coreSyncAvail(req: Request, res: Response, bff: BackForFront) {
    const transactionId: string = req.headers["gtw-transaction-id"].toString();
    const response = await bff.get(req.query);
    response.data["transactionId"] = transactionId;
    buildRedisCache(req, res, redis, response.data, bff);
    response.data.mechanisms = { async: false, redis: false };
    if (!!response.data.hotels === true) {
      const filteredAndOrdered = new FilteringAndOrdering(req.query, response.data.hotels).getFilteredAndOrdered();

      const metaBuilder = new MetaBuilder(1);

      try {
        metaBuilder["meta"]["nightsNumber"] = response.data.meta.nightsNumber;
      } catch (err) {}

      metaBuilder.comprise(response.data.hotels);
      metaBuilder.countHotels = filteredAndOrdered.length;

      const filteredAndOrderedPaging = new Paging(req.query, filteredAndOrdered).getPagingResults();
      injectValidateOne(req.query, filteredAndOrderedPaging);
      res.json({
        hotels: filteredAndOrderedPaging,
        meta: metaBuilder.getMeta(),
        mechanisms: response.data.mechanisms,
        transactionId,
      });
    } else {
      res.json(response.data);
    }
  }

  @Get("/hotels/booking/:source/:locatorCode")
  public async importLocator(req: Request, res: Response) {
    this.stateInit(req, "importlocator");
    const path = `booking/${req.params.source}/${req.params.locatorCode}`;
    try {
      const bff = new BackForFront(null, req, path);
      const response = await bff.get(req.query);
      res.json(response);
    } catch (error) {
      this.sendError(res, error);
    }
  }

  // IMPORTA LOC
  @Get("/hotels/booking/rooms/:source/:locatorCode")
  public async importLocatorRooms(req: Request, res: Response) {
    const operation: string = "GATEWAY:IMPORTALOC";
    const action = "gateway de hoteis";
    const transactionId: string = req.headers["gtw-transaction-id"].toString();

    let objLogger = {};
    const timer = new StopWatch();
    timer.start();

    try {
      const source = req.params.source;
      const locatorCode = req.params.locatorCode;
      const path = `booking/rooms/${source}/${locatorCode}`;
      const bff = new BackForFront(consul, req, path, true);

      const { url, agentSign, branchId } = this.getParams(bff);

      let duration = timer.getElapsedMilliseconds();

      objLogger = {
        operation,
        transactionId,
        duration,
        url,
        agentSign,
        branchId,
        source,
        locatorCode,
      };

      logger.info(objLogger, `Iniciando consulta ao ${action}`);

      const response = await bff.get(req.query);

      duration = timer.getElapsedMilliseconds();
      objLogger["duration"] = duration;
      objLogger["short_message"] = "success";
      logger.info(objLogger, `Consulta ao ${action} concluida com sucesso`);

      response.data.transactionId = transactionId;

      if (response.data) {
        const sanitizedresponse = JSON.parse(stringifyCircular(response.data));
        res.json(sanitizedresponse);
      } else {
        res.json(response);
      }
    } catch (error) {
      // this.responseError(error, objLogger, timer, action, res, transactionId);
      // this.sendError(res, error);

      const { message } = error;
      res.status(500);
      res.json({ message, transactionId });
    }
  }

  @Get("/health")
  public health(req: Request): { [index: string]: string | Date | string[] } {
    return {
      version: "1.6.0",
      date: new Date(),
      obs: [
        "Import Locator",
        "Master switch",
        "Loader issue",
        "Validate=1",
        "Amenities",
        "Redis SET EXPIRE and meta mealPlan",
        "Filter by UNION and several enhancements",
        "MetaBuilder",
        "Websocket should be working now",
        "Filtering and Ordering adjustments",
        "Filtering and Ordering",
        "Prices should be ordered by priceWithTax",
        "Generalized orderer repaired",
        "Swagger revisited, rooms ordered with generalized orderer now complies with collections",
        "Redis key generation revisited",
        "Request owner, production env vars",
        "agentSign, branchId from JWT to headers to conform OSB",
        "Consul switches",
        "Redis cache revisited",
        "Cumulative Websocket response",
        "Redis cache on async request",
        "Details, Rooms, HasAvail",
        "Deploy QA",
        "Sync and Async Avail",
        "Redis Mechanism, Consul",
        "WebSocket, Axios and Gateway request",
        "Initial commit",
      ],
    };
  }

  private stateInit(req, what) {
    const ownerDebug = req.headers["owner-debug"];
    const log = (ownerDebug && new Log().setOwner(ownerDebug)) || this.log;
    console.log("\n\n");
    return log.debug(`Starting ${what.toUpperCase()}`);
  }

  private sendError(res, error) {
    this.log.error(error.toString());
    res.status(500);
    res.json(error);
  }
}
