# Build Layer
FROM node:14.15-alpine3.13 AS build
WORKDIR /app
COPY ./ ./ 

RUN apk add --no-cache zip
RUN npm install 
RUN npm run build 
RUN zip sub-backend-hotels-store.zip ./ -r -x /node_modules/* 

# Veracode layer
FROM veracode/api-wrapper-java:latest AS veracode
ARG VERACODE_APP_ID
ARG VERACODE_API_KEY
ARG BUILD_ID
COPY --from=build /app /home/<USER>/app
RUN java -jar /opt/veracode/api-wrapper.jar \
         -vid $VERACODE_APP_ID \
         -vkey $VERACODE_API_KEY \
         -version $BUILD_ID \
         -action UploadAndScan \
         -createprofile true \
         -appname "sub-backend-hotels-store" \
         -filepath app/sub-backend-hotels-store.zip; exit 0;

# Runner layer
FROM node:14.15-alpine3.13
WORKDIR /app 
COPY --from=veracode /home/<USER>/app ./ 

RUN rm sub-backend-hotels-store.zip 

ENV PORT 80 \
    TZ America/Sao_Paulo

EXPOSE 80

ENTRYPOINT ["npm", "start"] 