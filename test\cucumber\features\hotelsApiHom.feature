Feature: Test rooms

  Scenario Outline: Test rooms
    Given I make a GET request api hom hotels with <checkIn>, <checkOut>, <itemsPerPage>, <packageGroup>, <rooms>, <zoneId>, <pageNumber>, <sortBy>, <sortOrder>, <maxPrice>, <minPrice>, <name>
    When I receive a response api hom hotels
    Then response api hom hotels should have a status 200
    Examples:
      | checkIn    | checkOut   | itemsPerPage | packageGroup | rooms   | zoneId | pageNumber | sortBy       | sortOrder | maxPrice | minPrice | name |
      | 2022-06-15 | 2022-06-16 | 50           | STANDALONE   | 30%2C30 | 9626   |  1         | preferential | ascending |          |          |      |
      | 2022-04-01 | 2022-04-05 | 25           | STANDALONE   | 30%2C30 | 7110   |  1         | preferential | ascending | 10217.3  | 536.31   | ibis |