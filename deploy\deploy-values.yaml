environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 200m
              limits:
                memory: 1Gi
                cpu: "2"
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            ports:
              - containerPort: 80
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 30
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 100
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-hotels-store
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_PORT
                value: "8500"
              - name: LOG_HOST
                value: logstash-ti.services.cvc.com.br
              - name: LOG_PORT
                value: "12201"
              - name: HOTEL_BACK_FOR_FRONT_OSB_URL
                value: http://hotel-back-for-front.k8s-ti-cvc.com.br/service
              - name: BROKERS
                value: Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC
              - name: CONSUL_URL
                value: consul-dev.services.cvc.com.br
              - name: HEADER_NAME_LABEL_REDIS_KEY
                value: gtw-search-token
              - name: PREFIX
                value: sub-backend-hotels-store
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: HOTEL_BACK_FOR_FRONT_GATEWAY_URL
                value: http://hotel-back-for-front.k8s-ti-cvc.com.br/service
              - name: CONSUL_KEY_FRANCHISE_SWITCH
                value: turned.off.stores
              - name: CONSUL_KEY_PREFIX_ID_AIM_OSB
                value: prefix.ids.aim.osb
              - name: HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY
                value: gtw-sec-websocket-key
              - name: CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH
                value: stores.importLoc
              - name: ADDITIONAL_OPTS
                value: " "
              - name: NODE_ENV
                value: ti
              - name: CONSUL_KEY_VALUE_TREE
                value: cvc-hosting-hotels-store
              - name: CONSUL_KEY_MASTER_SWITCH
                value: master.switch
              - name: HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN
                value: gtw-sec-user-token
              - name: VAULT_SCHEME
                value: http
              - name: WEBSOCKET_SERVER_URL
                value: https://websocket-dev.services.cvc.com.br
              - name: APP_NAME
                value: sub-backend-hotels-store
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 200m
              limits:
                memory: 1Gi
                cpu: "2"
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            ports:
              - containerPort: 80
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 30
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 100
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-hotels-store
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH
                value: stores.importLoc
              - name: ADDITIONAL_OPTS
                value: " "
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_KEY_MASTER_SWITCH
                value: master.switch
              - name: WEBSOCKET_SERVER_URL
                value: https://websocket-hom.services.cvc.com.br
              - name: CONSUL_HOST
                value: consul-qa.services.cvc.com.br
              - name: LOG_PORT
                value: "12201"
              - name: CONSUL_URL
                value: consul-qa.services.cvc.com.br
              - name: CONSUL_KEY_FRANCHISE_SWITCH
                value: stores.switch
              - name: BROKERS
                value: Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC
              - name: CONSUL_KEY_VALUE_TREE
                value: sub-backend-hotels-store
              - name: CONSUL_KEY_PREFIX_ID_AIM_OSB
                value: prefix.ids.aim.osb
              - name: PREFIX
                value: sub-backend-hotels-store
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: LOG_HOST
                value: logstash-qa.services.cvc.com.br
              - name: HOTEL_BACK_FOR_FRONT_GATEWAY_URL
                value: http://hotel-back-for-front.k8s-qa-cvc.com.br
              - name: HOTEL_BACK_FOR_FRONT_OSB_URL
                value: http://osb-qa.services.cvc.com.br
              - name: HEADER_NAME_LABEL_REDIS_KEY
                value: gtw-search-token
              - name: HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN
                value: gtw-sec-user-token
              - name: HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY
                value: gtw-sec-websocket-key
              - name: CONSUL_PORT
                value: "8500"
              - name: APP_NAME
                value: sub-backend-hotels-store
              - name: NODE_ENV
                value: qa
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 200m
              limits:
                memory: 1Gi
                cpu: "2"
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            ports:
              - containerPort: 80
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 30
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 100
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-hotels-store
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH
                value: stores.importLoc
              - name: ADDITIONAL_OPTS
                value: " "
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_KEY_MASTER_SWITCH
                value: master.switch
              - name: WEBSOCKET_SERVER_URL
                value: https://websocket-hom.services.cvc.com.br
              - name: CONSUL_HOST
                value: consul-qa.services.cvc.com.br
              - name: LOG_PORT
                value: "12201"
              - name: CONSUL_URL
                value: consul-qa.services.cvc.com.br
              - name: CONSUL_KEY_FRANCHISE_SWITCH
                value: stores.switch
              - name: BROKERS
                value: Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC
              - name: CONSUL_KEY_VALUE_TREE
                value: sub-backend-hotels-store
              - name: CONSUL_KEY_PREFIX_ID_AIM_OSB
                value: prefix.ids.aim.osb
              - name: PREFIX
                value: sub-backend-hotels-store
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: LOG_HOST
                value: logstash-qa.services.cvc.com.br
              - name: HOTEL_BACK_FOR_FRONT_GATEWAY_URL
                value: http://hotel-back-for-front.k8s-qa-cvc.com.br
              - name: HOTEL_BACK_FOR_FRONT_OSB_URL
                value: http://osb-qa.services.cvc.com.br
              - name: HEADER_NAME_LABEL_REDIS_KEY
                value: gtw-search-token
              - name: HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN
                value: gtw-sec-user-token
              - name: HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY
                value: gtw-sec-websocket-key
              - name: CONSUL_PORT
                value: "8500"
              - name: APP_NAME
                value: sub-backend-hotels-store
              - name: NODE_ENV
                value: qa
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 200m
              limits:
                memory: 1Gi
                cpu: "2"
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            ports:
              - containerPort: 80
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 30
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 100
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-hotels-store
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_KEY_VALUE_TREE
                value: sub-backend-hotels-store
              - name: HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY
                value: gtw-sec-websocket-key
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: BROKERS
                value: Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC
              - name: NODE_ENV
                value: prod
              - name: HOTEL_BACK_FOR_FRONT_GATEWAY_URL
                value: http://hotel-back-for-front.k8s-cvc.com.br
              - name: CONSUL_KEY_FRANCHISE_SWITCH
                value: stores.switch
              - name: CONSUL_KEY_MASTER_SWITCH
                value: master.switch
              - name: CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH
                value: stores.importLoc
              - name: PREFIX
                value: sub-backend-hotels-store
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_URL
                value: consul-prod.services.cvc.com.br
              - name: APP_NAME
                value: sub-backend-hotels-store
              - name: LOG_PORT
                value: "12201"
              - name: HOTEL_BACK_FOR_FRONT_OSB_URL
                value: https://api.aws.cvc.com.br/bookinghotel
              - name: HEADER_NAME_LABEL_REDIS_KEY
                value: gtw-search-token
              - name: HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN
                value: gtw-sec-user-token
              - name: WEBSOCKET_SERVER_URL
                value: https://b2b-websocket.services.cvc.com.br
              - name: ADDITIONAL_OPTS
                value: " "
              - name: LOG_HOST
                value: logstash.services.cvc.com.br
              - name: CONSUL_KEY_PREFIX_ID_AIM_OSB
                value: prefix.ids.aim.osb
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 200m
              limits:
                memory: 1Gi
                cpu: "2"
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 30
            ports:
              - containerPort: 80
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 30
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 80
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 100
          service:
            ports:
              - name: mainport
                port: 80
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-hotels-store
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: CONSUL_KEY_VALUE_TREE
                value: sub-backend-hotels-store
              - name: HEADER_NAME_LABEL_WEB_SOCKET_SERVER_KEY
                value: gtw-sec-websocket-key
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: BROKERS
                value: Extranet, Juniper, Omnibees, JuniperCorp, Hotelbeds, CVC
              - name: NODE_ENV
                value: prod
              - name: HOTEL_BACK_FOR_FRONT_GATEWAY_URL
                value: http://hotel-back-for-front.k8s-cvc.com.br
              - name: CONSUL_KEY_FRANCHISE_SWITCH
                value: stores.switch
              - name: CONSUL_KEY_MASTER_SWITCH
                value: master.switch
              - name: CONSUL_KEY_IMPORT_LOC_FRANCHISE_SWITCH
                value: stores.importLoc
              - name: PREFIX
                value: sub-backend-hotels-store
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_URL
                value: consul-prod.services.cvc.com.br
              - name: APP_NAME
                value: sub-backend-hotels-store
              - name: LOG_PORT
                value: "12201"
              - name: HOTEL_BACK_FOR_FRONT_OSB_URL
                value: https://api.aws.cvc.com.br/bookinghotel
              - name: HEADER_NAME_LABEL_REDIS_KEY
                value: gtw-search-token
              - name: HEADER_NAME_LABEL_JWT_SEC_USER_TOKEN
                value: gtw-sec-user-token
              - name: WEBSOCKET_SERVER_URL
                value: https://b2b-websocket.services.cvc.com.br
              - name: ADDITIONAL_OPTS
                value: " "
              - name: LOG_HOST
                value: logstash.services.cvc.com.br
              - name: CONSUL_KEY_PREFIX_ID_AIM_OSB
                value: prefix.ids.aim.osb
