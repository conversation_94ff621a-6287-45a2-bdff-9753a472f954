import ConsulClient from "consul";
import { getEnv } from "../utils";
import { injectable, inject } from "inversify";
import { TYPES } from "../config/types";
import Log from "../config/log";

type CallbackInitialized = () => void;

class Consul {
  private consulName = `${getEnv("CONSUL_KEY_VALUE_TREE")}/`;
  private consul;
  private keysAndValues = {};
  private log = new Log();
  private callback: CallbackInitialized;

  constructor() {
    this.log.debug("Consul instanced");
    this.#initialize();
    this.#loadKeysAndWatchThem();
  }

  query(key: string) {
    const builtKey = `${this.consulName}${key}`;
    const value = this.keysAndValues[builtKey];

    if (value === undefined) {
      this.log.error(`Could not fetch key: ${key} from Consul`);
      return null;
    }
    return value;
  }

  #initialize() {
    const hostAndPort = {
      host: getEnv("CONSUL_URL"),
      port: getEnv("CONSUL_PORT"),
    };

    this.consul = new ConsulClient(hostAndPort);
  }

  //Pega todas as keys e coloca os watchers
  #loadKeysAndWatchThem() {
    try {
      this.consul.kv.keys(this.consulName, (error, result) => {
        if (!!error === true) {
          this.log.error("Could NOT retrieve keys from Consul", error);
          return;
        }

        this.log.debug(result, "Consul keys retrieved");
        let counter = 0;
        for (let i = 0; i < result.length; i++) {
          const key = result[i];
          this.consul.kv.get(
            {
              key: key,
              recurse: true,
            },
            (error, value) => {
              ++counter;
              if (!!error === true) {
                this.log.error(
                  `Could not retrieve key: ${key} from Consul`,
                  error.toString()
                );
              }

              console.log("value", value);
              this.keysAndValues[key] = value[0]?.Value;
              this.#startWatching(key);

              if (counter >= result.length) {
                this.callback && this.callback();
              }
            }
          );
        }
      });
    } catch (err) {
		console.log("err", err)
	}
  }

  #startWatching(key) {
    const watch = this.consul.watch({
      method: this.consul.kv.get,
      options: { key: key },
    });

    watch.on("change", (data, res) => {
      if (!!data === false) {
        this.log.error("Consul watch on change, no data has arrived");
        return;
      }
      this.log.debug({ key: data.Key, value: data.Value }, "Watching Consul");
      this.keysAndValues[data.Key] = data.Value;
    });

    watch.on("error", (error) => {
      this.log.error(error.toString());
    });
  }

  public setCallbackInitialized(callback: CallbackInitialized) {
    this.callback = callback;
  }
}

export default new Consul();
export { Consul };
