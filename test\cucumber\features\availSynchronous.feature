Feature: Test avail

  Scenario Outline: Test avail synchronous
    Given I make a GET avail synchronous request with <checkIn>, <checkOut>, <packageGroup>, <rooms>, <zoneId>
    When I receive a response avail synchronous
    Then response avail synchronous should have a status 200
    Examples:
      | checkIn    | checkOut    | packageGroup       | rooms   | zoneId |
      | 2022-03-05 | 2022-03-07  |   STANDALONE  git  | 30%2C30 | 1463   |