Feature: Test avail

  Scenario Outline: Test avail
    Given I make a GET request with <checkIn>, <checkOut>, <itemsPerPage>, <packageGroup>, <pageNumber>, <rooms>, <sortBy>, <sortOrder>, <zoneId>
    When I receive a response
    Then response should have a status 200
    Examples:
      | checkIn    | checkOut    | itemsPerPage | packageGroup    | pageNumber | rooms   | sortBy       | sortOrder | zoneId |
      | 2022-03-05 | 2022-03-07  |  25          | STANDALONE  git |  1         | 30%2C30 | preferential | ascending | 1463   |

  Scenario Outline: Test avail - Validate time request
    Given I make a GET request with <checkIn>, <checkOut>, <itemsPerPage>, <packageGroup>, <pageNumber>, <rooms>, <sortBy>, <sortOrder>, <zoneId>
    When I receive a response
    Then response should have a status 200
    And should return response within cache time
    Examples:
      | checkIn    | checkOut    | itemsPerPage | packageGroup    | pageNumber | rooms   | sortBy       | sortOrder | zoneId |
      | 2022-03-05 | 2022-03-07  |  25          | STANDALONE  git |  1         | 30%2C30 | preferential | ascending | 1463   |